const express = require('express');
const { firestoreHelpers, collections } = require('../config/firebase');
const { validationSets } = require('../middleware/validation');
const SchemaValidator = require('../utils/schemaValidator');
const router = express.Router();

// @desc    Create a new booking
// @route   POST /api/bookings
// @access  Public
router.post('/', validationSets.createBooking, async (req, res) => {
  try {
    const bookingData = {
      ...req.body,
      status: 'pending',
      bookingNumber: generateBookingNumber(),
      estimatedCost: calculateEstimatedCost(req.body),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Validate against schema
    const validation = SchemaValidator.validateBooking(bookingData);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Booking data validation failed',
          details: validation.errors
        }
      });
    }

    // Sanitize data
    const sanitizedData = SchemaValidator.sanitize(bookingData, require('../models/schemas').bookingSchema);

    const bookingId = await firestoreHelpers.addDocument(collections.BOOKINGS, sanitizedData);

    res.status(201).json({
      success: true,
      message: 'Booking created successfully',
      data: {
        id: bookingId,
        bookingNumber: sanitizedData.bookingNumber,
        status: sanitizedData.status,
        estimatedCost: sanitizedData.estimatedCost
      }
    });
  } catch (error) {
    console.error('Error creating booking:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create booking',
        details: error.message
      }
    });
  }
});

// @desc    Get all bookings
// @route   GET /api/bookings
// @access  Public (should be protected in production)
router.get('/', validationSets.getWithQuery, async (req, res) => {
  try {
    const { 
      limit = 50, 
      offset = 0, 
      sort = 'createdAt', 
      order = 'desc',
      status,
      eventType,
      dateFrom,
      dateTo
    } = req.query;

    let conditions = [];

    // Add filters
    if (status) {
      conditions.push({ field: 'status', operator: '==', value: status });
    }
    if (eventType) {
      conditions.push({ field: 'eventType', operator: '==', value: eventType });
    }
    if (dateFrom) {
      conditions.push({ field: 'eventDate', operator: '>=', value: dateFrom });
    }
    if (dateTo) {
      conditions.push({ field: 'eventDate', operator: '<=', value: dateTo });
    }

    const bookings = await firestoreHelpers.queryDocuments(
      collections.BOOKINGS,
      conditions,
      parseInt(limit),
      { field: sort, direction: order }
    );

    // Apply offset manually
    const offsetBookings = bookings.slice(parseInt(offset));

    res.json({
      success: true,
      count: offsetBookings.length,
      total: bookings.length,
      data: offsetBookings
    });
  } catch (error) {
    console.error('Error fetching bookings:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch bookings',
        details: error.message
      }
    });
  }
});

// @desc    Get booking by ID
// @route   GET /api/bookings/:id
// @access  Public
router.get('/:id', validationSets.getById, async (req, res) => {
  try {
    const { id } = req.params;
    const booking = await firestoreHelpers.getDocument(collections.BOOKINGS, id);

    if (!booking) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Booking not found'
        }
      });
    }

    res.json({
      success: true,
      data: booking
    });
  } catch (error) {
    console.error('Error fetching booking:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch booking',
        details: error.message
      }
    });
  }
});

// @desc    Update booking
// @route   PUT /api/bookings/:id
// @access  Public (should be protected in production)
router.put('/:id', validationSets.updateBooking, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if booking exists
    const existingBooking = await firestoreHelpers.getDocument(collections.BOOKINGS, id);
    if (!existingBooking) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Booking not found'
        }
      });
    }

    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    // Recalculate estimated cost if relevant fields changed
    if (req.body.services || req.body.guestCount || req.body.eventType) {
      updateData.estimatedCost = calculateEstimatedCost({
        ...existingBooking,
        ...req.body
      });
    }

    await firestoreHelpers.updateDocument(collections.BOOKINGS, id, updateData);

    res.json({
      success: true,
      message: 'Booking updated successfully',
      data: {
        id,
        ...updateData
      }
    });
  } catch (error) {
    console.error('Error updating booking:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update booking',
        details: error.message
      }
    });
  }
});

// @desc    Update booking status
// @route   PATCH /api/bookings/:id/status
// @access  Public (should be protected in production)
router.patch('/:id/status', validationSets.getById, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    const validStatuses = ['pending', 'confirmed', 'in-progress', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid status',
          validStatuses
        }
      });
    }

    const updateData = {
      status,
      updatedAt: new Date().toISOString()
    };

    if (notes) {
      updateData.statusNotes = notes;
    }

    await firestoreHelpers.updateDocument(collections.BOOKINGS, id, updateData);

    res.json({
      success: true,
      message: 'Booking status updated successfully',
      data: {
        id,
        status,
        updatedAt: updateData.updatedAt
      }
    });
  } catch (error) {
    console.error('Error updating booking status:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update booking status',
        details: error.message
      }
    });
  }
});

// @desc    Delete booking
// @route   DELETE /api/bookings/:id
// @access  Public (should be protected in production)
router.delete('/:id', validationSets.getById, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if booking exists
    const existingBooking = await firestoreHelpers.getDocument(collections.BOOKINGS, id);
    if (!existingBooking) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Booking not found'
        }
      });
    }

    await firestoreHelpers.deleteDocument(collections.BOOKINGS, id);

    res.json({
      success: true,
      message: 'Booking deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting booking:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to delete booking',
        details: error.message
      }
    });
  }
});

// Helper functions
function generateBookingNumber() {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `EVT-${timestamp.slice(-6)}-${random}`;
}

function calculateEstimatedCost(bookingData) {
  // Basic cost calculation logic
  let baseCost = 10000; // Base cost in INR
  
  // Adjust based on guest count
  const guestCount = parseInt(bookingData.guestCount?.split('-')[0] || '1');
  if (guestCount > 50) baseCost *= 2;
  else if (guestCount > 20) baseCost *= 1.5;
  
  // Adjust based on services
  const serviceMultiplier = (bookingData.services?.length || 1) * 0.3;
  baseCost += baseCost * serviceMultiplier;
  
  // Adjust based on event type
  const eventTypeMultipliers = {
    'Corporate': 1.5,
    'Conference': 2.0,
    'Birthday': 1.0,
    'HouseWarming': 1.2
  };
  
  baseCost *= eventTypeMultipliers[bookingData.eventType] || 1.0;
  
  return Math.round(baseCost);
}

module.exports = router;
