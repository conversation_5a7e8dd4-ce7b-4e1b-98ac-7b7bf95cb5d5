#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

async function fixNgrok() {
  console.log('🔧 Ngrok Troubleshooting and Fix Tool\n');
  
  try {
    // Step 1: Check if ngrok is installed globally
    console.log('1. Checking ngrok installation...');
    await checkNgrokInstallation();
    
    // Step 2: Kill any existing ngrok processes
    console.log('\n2. Cleaning up existing ngrok processes...');
    await killNgrokProcesses();
    
    // Step 3: Check ngrok configuration
    console.log('\n3. Checking ngrok configuration...');
    await checkNgrokConfig();
    
    // Step 4: Test ngrok connection
    console.log('\n4. Testing ngrok connection...');
    await testNgrokConnection();
    
    console.log('\n✅ Ngrok troubleshooting complete!');
    console.log('\n📋 Try running your tunnel again:');
    console.log('   npm run dev:ngrok');
    
  } catch (error) {
    console.error('\n❌ Troubleshooting failed:', error.message);
    console.log('\n💡 Alternative solutions:');
    console.log('1. Use LocalTunnel instead: npm run dev:localtunnel');
    console.log('2. Install ngrok globally: npm install -g ngrok');
    console.log('3. Use Cloudflare tunnel: npm run dev:cloudflare');
  }
}

function checkNgrokInstallation() {
  return new Promise((resolve, reject) => {
    // Check if ngrok is available via npx
    const ngrok = spawn('npx', ['ngrok', '--version'], { stdio: 'pipe' });
    
    let output = '';
    ngrok.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    ngrok.stderr.on('data', (data) => {
      output += data.toString();
    });
    
    ngrok.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ Ngrok is available: ${output.trim()}`);
        resolve();
      } else {
        console.log('❌ Ngrok not found via npx');
        console.log('💡 Installing ngrok locally...');
        installNgrokLocally().then(resolve).catch(reject);
      }
    });
    
    ngrok.on('error', (error) => {
      console.log('❌ Error checking ngrok:', error.message);
      installNgrokLocally().then(resolve).catch(reject);
    });
    
    setTimeout(() => {
      ngrok.kill();
      reject(new Error('Ngrok check timeout'));
    }, 10000);
  });
}

function installNgrokLocally() {
  return new Promise((resolve, reject) => {
    console.log('📦 Installing ngrok locally...');
    
    const npm = spawn('npm', ['install', 'ngrok@latest'], { 
      stdio: 'pipe',
      cwd: path.join(__dirname, '..')
    });
    
    npm.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    
    npm.stderr.on('data', (data) => {
      process.stderr.write(data);
    });
    
    npm.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Ngrok installed successfully');
        resolve();
      } else {
        reject(new Error(`Ngrok installation failed with code ${code}`));
      }
    });
    
    npm.on('error', (error) => {
      reject(new Error(`Failed to install ngrok: ${error.message}`));
    });
  });
}

function killNgrokProcesses() {
  return new Promise((resolve) => {
    const ngrok = require('../node_modules/ngrok');
    
    ngrok.kill().then(() => {
      console.log('✅ Killed existing ngrok processes');
      resolve();
    }).catch((error) => {
      console.log('⚠️  No ngrok processes to kill or error:', error.message);
      resolve();
    });
  });
}

function checkNgrokConfig() {
  return new Promise((resolve) => {
    const configPath = path.join(os.homedir(), '.ngrok2', 'ngrok.yml');
    
    if (fs.existsSync(configPath)) {
      console.log(`✅ Ngrok config found at: ${configPath}`);
      
      try {
        const config = fs.readFileSync(configPath, 'utf8');
        if (config.includes('authtoken')) {
          console.log('✅ Auth token configured');
        } else {
          console.log('⚠️  No auth token found in config');
          console.log('💡 You can add one at: https://dashboard.ngrok.com/get-started/your-authtoken');
        }
      } catch (error) {
        console.log('⚠️  Could not read ngrok config');
      }
    } else {
      console.log('⚠️  No ngrok config found (this is normal for first-time use)');
      console.log('💡 Ngrok will create one automatically');
    }
    
    resolve();
  });
}

function testNgrokConnection() {
  return new Promise((resolve, reject) => {
    console.log('🔄 Testing ngrok connection...');
    
    const ngrok = spawn('npx', ['ngrok', 'http', '8080', '--log', 'stdout'], { 
      stdio: 'pipe'
    });
    
    let connected = false;
    let output = '';
    
    ngrok.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      
      if (text.includes('started tunnel') || text.includes('https://')) {
        connected = true;
        console.log('✅ Ngrok connection test successful');
        ngrok.kill();
        resolve();
      }
    });
    
    ngrok.stderr.on('data', (data) => {
      output += data.toString();
    });
    
    ngrok.on('close', (code) => {
      if (!connected) {
        console.log('❌ Ngrok connection test failed');
        console.log('Output:', output);
        reject(new Error('Ngrok connection test failed'));
      }
    });
    
    ngrok.on('error', (error) => {
      reject(new Error(`Ngrok test error: ${error.message}`));
    });
    
    // Kill test after 15 seconds
    setTimeout(() => {
      if (!connected) {
        ngrok.kill();
        reject(new Error('Ngrok connection test timeout'));
      }
    }, 15000);
  });
}

fixNgrok();
