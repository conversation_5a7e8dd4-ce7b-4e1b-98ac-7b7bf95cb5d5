[debug] [2025-07-27T19:08:40.843Z] ----------------------------------------------------------------------
[debug] [2025-07-27T19:08:40.847Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js login
[debug] [2025-07-27T19:08:40.848Z] CLI Version:   14.11.1
[debug] [2025-07-27T19:08:40.848Z] Platform:      win32
[debug] [2025-07-27T19:08:40.848Z] Node Version:  v22.17.0
[debug] [2025-07-27T19:08:40.848Z] Time:          Mon Jul 28 2025 00:38:40 GMT+0530 (India Standard Time)
[debug] [2025-07-27T19:08:40.849Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-27T19:08:40.852Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[info] i  The Firebase CLI’s MCP server feature can optionally make use of Gemini in Firebase. Learn more about Gemini in Firebase and how it uses your data: https://firebase.google.com/docs/gemini-in-firebase#how-gemini-in-firebase-uses-your-data 
[debug] [2025-07-27T19:08:41.552Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-07-27T19:08:41.552Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":*************,"node8WarnAfter":*************}
[info] 
[info] i  Firebase optionally collects CLI and Emulator Suite usage and error reporting information to help improve our products. Data is collected in accordance with Google's privacy policy (https://policies.google.com/privacy) and is not used to identify you. 
[info] 
[info] i  To change your the preference at any time, run `firebase logout` and `firebase login` again. 
[info] 
[info] Visit this URL on this device to log in:
[info] https://accounts.google.com/o/oauth2/auth?client_id=************-fgrhgmd47bqnekij5i8b5pr03ho849e6.apps.googleusercontent.com&scope=email%20openid%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloudplatformprojects.readonly%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Ffirebase%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-platform&response_type=code&state=*********&redirect_uri=http%3A%2F%2Flocalhost%3A9005
[info] 
[info] Waiting for authentication...
