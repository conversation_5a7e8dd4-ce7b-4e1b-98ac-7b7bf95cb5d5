// Simple test script to verify API connection
const API_BASE_URL = 'http://*************:5000/api';

async function testApiConnection() {
  console.log('Testing API connection to:', API_BASE_URL);
  
  try {
    const response = await fetch(`${API_BASE_URL}/services`);
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('API connection successful!');
      console.log('Number of services:', data.data?.length || 0);
      console.log('First service:', data.data?.[0]?.title || 'No services found');
    } else {
      console.error('API request failed with status:', response.status);
      const errorText = await response.text();
      console.error('Error response:', errorText);
    }
  } catch (error) {
    console.error('Network error:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testApiConnection();
