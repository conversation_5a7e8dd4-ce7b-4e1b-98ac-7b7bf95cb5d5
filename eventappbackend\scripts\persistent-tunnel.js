#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class PersistentTunnel {
  constructor() {
    this.configFile = path.join(__dirname, '..', '.persistent-tunnel.json');
    this.tunnelProcess = null;
    this.tunnelUrl = null;
  }

  // Load saved tunnel configuration
  loadConfig() {
    try {
      if (fs.existsSync(this.configFile)) {
        const config = JSON.parse(fs.readFileSync(this.configFile, 'utf8'));
        return config;
      }
    } catch (error) {
      console.warn('Could not load tunnel config:', error.message);
    }
    return {};
  }

  // Save tunnel configuration
  saveConfig(config) {
    try {
      fs.writeFileSync(this.configFile, JSON.stringify(config, null, 2));
      console.log('✅ Tunnel configuration saved');
    } catch (error) {
      console.warn('Could not save tunnel config:', error.message);
    }
  }

  // Start serveo tunnel with custom subdomain
  async startServeoTunnel(port = 5000, subdomain = 'eventapp-backend') {
    return new Promise((resolve, reject) => {
      console.log('🚇 Starting Serveo tunnel...');
      console.log(`📡 Port: ${port}`);
      console.log(`🌐 Subdomain: ${subdomain}`);

      // Use SSH to create tunnel via serveo.net
      const sshCommand = `ssh -R ${subdomain}:80:localhost:${port} serveo.net`;
      
      console.log(`🔧 Command: ${sshCommand}`);
      
      this.tunnelProcess = spawn('ssh', [
        '-R', `${subdomain}:80:localhost:${port}`,
        'serveo.net'
      ], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let tunnelEstablished = false;

      this.tunnelProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log('📡 Serveo:', output.trim());
        
        // Look for the tunnel URL in the output
        const urlMatch = output.match(/https:\/\/[^\s]+/);
        if (urlMatch && !tunnelEstablished) {
          this.tunnelUrl = urlMatch[0];
          tunnelEstablished = true;
          
          console.log('✅ Serveo tunnel established!');
          console.log(`🌐 URL: ${this.tunnelUrl}`);
          
          // Save configuration
          this.saveConfig({
            type: 'serveo',
            url: this.tunnelUrl,
            subdomain: subdomain,
            port: port,
            timestamp: new Date().toISOString()
          });
          
          resolve(this.tunnelUrl);
        }
      });

      this.tunnelProcess.stderr.on('data', (data) => {
        const error = data.toString();
        console.error('❌ Serveo error:', error.trim());
        
        if (error.includes('Permission denied') || error.includes('Connection refused')) {
          console.log('🔄 Trying alternative approach...');
          this.startLocalTunnelWithCustomDomain(port, subdomain);
        }
      });

      this.tunnelProcess.on('close', (code) => {
        console.log(`🛑 Serveo tunnel closed with code ${code}`);
        if (!tunnelEstablished) {
          console.log('🔄 Falling back to LocalTunnel...');
          this.startLocalTunnelWithCustomDomain(port, subdomain);
        }
      });

      this.tunnelProcess.on('error', (error) => {
        console.error('❌ Failed to start Serveo tunnel:', error.message);
        console.log('🔄 Falling back to LocalTunnel...');
        this.startLocalTunnelWithCustomDomain(port, subdomain);
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!tunnelEstablished) {
          console.log('⏰ Serveo timeout, falling back to LocalTunnel...');
          this.tunnelProcess.kill();
          this.startLocalTunnelWithCustomDomain(port, subdomain);
        }
      }, 30000);
    });
  }

  // Fallback to LocalTunnel with custom subdomain
  async startLocalTunnelWithCustomDomain(port = 5000, subdomain = 'eventapp-backend') {
    return new Promise((resolve, reject) => {
      console.log('🚇 Starting LocalTunnel with custom subdomain...');
      
      this.tunnelProcess = spawn('npx', [
        'localtunnel',
        '--port', port.toString(),
        '--subdomain', subdomain
      ], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let tunnelEstablished = false;

      this.tunnelProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log('📡 LocalTunnel:', output.trim());
        
        // Look for the tunnel URL
        const urlMatch = output.match(/https:\/\/[^\s]+/);
        if (urlMatch && !tunnelEstablished) {
          this.tunnelUrl = urlMatch[0];
          tunnelEstablished = true;
          
          console.log('✅ LocalTunnel established!');
          console.log(`🌐 URL: ${this.tunnelUrl}`);
          
          // Save configuration
          this.saveConfig({
            type: 'localtunnel',
            url: this.tunnelUrl,
            subdomain: subdomain,
            port: port,
            timestamp: new Date().toISOString()
          });
          
          resolve(this.tunnelUrl);
        }
      });

      this.tunnelProcess.stderr.on('data', (data) => {
        console.error('❌ LocalTunnel error:', data.toString().trim());
      });

      this.tunnelProcess.on('close', (code) => {
        console.log(`🛑 LocalTunnel closed with code ${code}`);
      });

      this.tunnelProcess.on('error', (error) => {
        console.error('❌ Failed to start LocalTunnel:', error.message);
        reject(error);
      });
    });
  }

  // Stop tunnel
  stop() {
    if (this.tunnelProcess) {
      console.log('🛑 Stopping tunnel...');
      this.tunnelProcess.kill();
      this.tunnelProcess = null;
    }
  }

  // Get current tunnel URL
  getCurrentUrl() {
    const config = this.loadConfig();
    return config.url || null;
  }
}

// CLI interface
if (require.main === module) {
  const tunnel = new PersistentTunnel();
  
  const command = process.argv[2];
  const port = parseInt(process.argv[3]) || 5000;
  const subdomain = process.argv[4] || 'eventapp-backend-stable';

  switch (command) {
    case 'start':
      tunnel.startServeoTunnel(port, subdomain)
        .then(url => {
          console.log(`\n🎉 Tunnel is ready!`);
          console.log(`🌐 Your API is accessible at: ${url}/api`);
          console.log(`📱 Use this URL in your frontend application`);
          console.log(`\n⏳ Press Ctrl+C to stop the tunnel\n`);
          
          // Keep the process running
          process.on('SIGINT', () => {
            tunnel.stop();
            process.exit(0);
          });
        })
        .catch(error => {
          console.error('❌ Failed to start tunnel:', error);
          process.exit(1);
        });
      break;
      
    case 'stop':
      tunnel.stop();
      break;
      
    case 'status':
      const url = tunnel.getCurrentUrl();
      if (url) {
        console.log(`✅ Tunnel URL: ${url}`);
      } else {
        console.log('❌ No active tunnel found');
      }
      break;
      
    default:
      console.log('Usage: node persistent-tunnel.js <start|stop|status> [port] [subdomain]');
      console.log('Example: node persistent-tunnel.js start 5000 my-api');
      break;
  }
}

module.exports = PersistentTunnel;
