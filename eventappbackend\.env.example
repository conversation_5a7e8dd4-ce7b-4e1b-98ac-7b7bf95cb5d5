# Server Configuration
PORT=5000
NODE_ENV=development

# Firebase Configuration
FIREBASE_PROJECT_ID=easemyevent-27729
FIREBASE_DATABASE_URL=https://easemyevent-27729-default-rtdb.firebaseio.com/
FIREBASE_SERVICE_ACCOUNT_KEY=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


# Alternative: Path to service account key file
# GOOGLE_APPLICATION_CREDENTIALS=./config/serviceAccountKey.json

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081,https://your-frontend-domain.com

# API Configuration
API_VERSION=v1
MAX_REQUEST_SIZE=10mb

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
