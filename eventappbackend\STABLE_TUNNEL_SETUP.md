# Stable Tunnel Setup for Event App Backend

This guide shows you how to run your backend with a **constant URL** that requires **no authentication** and works from anywhere.

## 🌐 Your Stable API URL

```
https://eventapp-api-stable.loca.lt/api
```

This URL will remain the same every time you restart the tunnel with the same subdomain.

## 🚀 Quick Start

### Option 1: Using npm script (Recommended)
```bash
cd eventappbackend
npm run dev:stable
```

### Option 2: Manual setup
```bash
# Terminal 1: Start backend server
cd eventappbackend
npm run dev

# Terminal 2: Start tunnel (in another terminal)
cd eventappbackend
npx localtunnel --port 5000 --subdomain eventapp-api-stable
```

### Option 3: Using batch file (Windows)
```bash
cd eventappbackend
start-with-tunnel.bat
```

## 📱 Frontend Configuration

Your React Native app is already configured to use the stable tunnel URL:

**File: `eventapp/constants/Api.ts`**
```typescript
const getApiBaseUrl = () => {
  if (!__DEV__) {
    return 'https://your-production-api.com/api'; // Production URL
  }
  
  // Development URLs - Use stable tunnel URL for universal access
  // This tunnel URL is consistent and works from anywhere (web, mobile, etc.)
  // No authentication required - just restart tunnel with same subdomain
  return 'https://eventapp-api-stable.loca.lt/api';
};
```

## ✅ Testing the Setup

### Test API Connection
```bash
cd eventapp
node test-api.js
```

Expected output:
```
Testing API connection to: https://eventapp-api-stable.loca.lt/api
Response status: 200
API connection successful!
Number of services: 12
First service: Cake
```

### Test in React Native App
1. Start the backend with tunnel: `npm run dev:stable`
2. Start Expo: `npx expo start`
3. Open the app and navigate to Services page
4. If there are errors, click "Debug API" button to see detailed logs

## 🔧 How It Works

1. **LocalTunnel** creates a secure tunnel from your local port 5000 to the internet
2. **Subdomain**: `eventapp-api-stable` ensures the URL stays consistent
3. **No Auth**: LocalTunnel doesn't require account setup or authentication
4. **CORS**: Backend is configured to allow requests from the tunnel URL

## 🛠️ Troubleshooting

### If tunnel URL changes
The URL might change if:
- Someone else uses the same subdomain
- LocalTunnel service restarts

**Solution**: Use a more unique subdomain:
```bash
npx localtunnel --port 5000 --subdomain your-unique-name-eventapp-api
```

Then update the URL in `eventapp/constants/Api.ts`

### If you get "connection refused" errors
1. Make sure backend server is running on port 5000
2. Check if another process is using port 5000:
   ```bash
   netstat -ano | findstr :5000
   ```
3. Restart both server and tunnel

### If CORS errors occur
The backend is already configured for tunnel URLs, but if you change the subdomain, update the CORS settings in `eventappbackend/.env`:

```env
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081,http://localhost:8082,https://your-new-subdomain.loca.lt
```

## 📋 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev:stable` | Start backend + tunnel together |
| `npm run dev` | Start backend only |
| `npm run tunnel:localtunnel` | Start tunnel only |
| `node start-with-tunnel.js` | Cross-platform start script |

## 🌍 Benefits of This Setup

✅ **Constant URL** - Same URL every time you restart
✅ **No Authentication** - No need for ngrok accounts or tokens  
✅ **Universal Access** - Works from web, mobile, anywhere
✅ **Easy Setup** - One command to start everything
✅ **Free** - No cost for basic usage
✅ **HTTPS** - Secure connection by default

## 📱 Mobile Testing

The tunnel URL works perfectly for mobile testing:
- **Expo Go**: Scan QR code and app will connect to tunnel
- **Physical Device**: Works on any network
- **Emulator**: Works without network configuration

## 🔄 Restarting

To restart everything:
1. Press `Ctrl+C` to stop current processes
2. Run `npm run dev:stable` again
3. The same URL will be available immediately

Your API will be accessible at: **https://eventapp-api-stable.loca.lt/api**
