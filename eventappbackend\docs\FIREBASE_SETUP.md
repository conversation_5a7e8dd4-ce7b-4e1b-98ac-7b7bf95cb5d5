# Firebase Setup Guide

This guide will help you set up Firebase authentication for your Event App Backend.

## Prerequisites

1. A Google account
2. Node.js installed on your machine
3. Firebase CLI (optional but recommended)

## Step 1: Create Firebase Project

1. **Go to Firebase Console:**
   - Visit [https://console.firebase.google.com/](https://console.firebase.google.com/)
   - Sign in with your Google account

2. **Create a new project:**
   - Click "Create a project"
   - Enter project name: `eventapp` (or your preferred name)
   - Enable Google Analytics (optional)
   - Click "Create project"

3. **Enable Firestore:**
   - In your project dashboard, click "Firestore Database"
   - Click "Create database"
   - Choose "Start in test mode" (we'll add security rules later)
   - Select a location (choose closest to your users)

## Step 2: Get Firebase Credentials

### Option A: Service Account Key (Recommended for Development)

1. **Generate Service Account Key:**
   - Go to Project Settings (gear icon) → Service accounts
   - Click "Generate new private key"
   - Download the JSON file
   - **Keep this file secure and never commit it to version control**

2. **Set up credentials:**
   ```bash
   # Create config directory
   mkdir config
   
   # Copy your downloaded service account key to config/serviceAccountKey.json
   cp ~/Downloads/your-project-firebase-adminsdk-xxxxx.json config/serviceAccountKey.json
   ```

3. **Update .env file:**
   ```env
   FIREBASE_PROJECT_ID=your-project-id
   GOOGLE_APPLICATION_CREDENTIALS=./config/serviceAccountKey.json
   ```

### Option B: Environment Variable (Recommended for Production)

1. **Get the service account key content:**
   - Open the downloaded JSON file
   - Copy the entire JSON content

2. **Set environment variable:**
   ```env
   FIREBASE_PROJECT_ID=your-project-id
   FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your-project-id",...}
   ```

### Option C: Google Cloud SDK (For Local Development)

1. **Install Google Cloud SDK:**
   - Download from [https://cloud.google.com/sdk/docs/install](https://cloud.google.com/sdk/docs/install)
   - Follow installation instructions for your OS

2. **Authenticate:**
   ```bash
   gcloud auth application-default login
   ```

3. **Set project:**
   ```bash
   gcloud config set project your-project-id
   ```

4. **Update .env file:**
   ```env
   FIREBASE_PROJECT_ID=your-project-id
   # No need for GOOGLE_APPLICATION_CREDENTIALS
   ```

## Step 3: Configure Environment Variables

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit .env file with your Firebase details:**
   ```env
   # Server Configuration
   PORT=5000
   NODE_ENV=development

   # Firebase Configuration
   FIREBASE_PROJECT_ID=your-actual-project-id
   FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

   # Choose ONE of the following authentication methods:

   # Option A: Service Account Key File
   GOOGLE_APPLICATION_CREDENTIALS=./config/serviceAccountKey.json

   # Option B: Service Account Key as Environment Variable
   # FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}

   # CORS Configuration
   ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081

   # API Configuration
   API_VERSION=v1
   MAX_REQUEST_SIZE=10mb
   ```

## Step 4: Test the Connection

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Test Firebase connection:**
   ```bash
   npm run validate-schema
   ```

3. **Initialize database:**
   ```bash
   npm run init-db
   ```

4. **Start the server:**
   ```bash
   npm run dev
   ```

5. **Test health endpoint:**
   ```bash
   curl http://localhost:5000/api/health
   ```

## Step 5: Deploy Security Rules

1. **Install Firebase CLI (if not already installed):**
   ```bash
   npm install -g firebase-tools
   ```

2. **Login to Firebase:**
   ```bash
   firebase login
   ```

3. **Initialize Firebase in your project:**
   ```bash
   firebase init firestore
   ```
   - Select your existing project
   - Use `firestore.rules` for Firestore rules
   - Use `firestore.indexes.json` for Firestore indexes

4. **Deploy security rules:**
   ```bash
   firebase deploy --only firestore:rules
   ```

## Troubleshooting

### Error: "Could not load the default credentials"

**Solution 1: Check environment variables**
```bash
# Verify your .env file has the correct values
cat .env | grep FIREBASE
```

**Solution 2: Verify service account key**
```bash
# Check if the service account key file exists and is valid JSON
cat config/serviceAccountKey.json | jq .
```

**Solution 3: Use explicit credentials**
```javascript
// In config/firebase.js, you can also initialize with explicit credentials:
const serviceAccount = require('../config/serviceAccountKey.json');
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: process.env.FIREBASE_PROJECT_ID
});
```

### Error: "Permission denied"

**Solution: Check Firestore rules**
- Go to Firebase Console → Firestore Database → Rules
- Ensure rules allow read/write access for testing:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true; // Temporary for testing
    }
  }
}
```

### Error: "Project not found"

**Solution: Verify project ID**
- Check your Firebase project ID in the console
- Ensure FIREBASE_PROJECT_ID in .env matches exactly

## Security Best Practices

1. **Never commit credentials to version control:**
   ```bash
   # Add to .gitignore
   echo "config/serviceAccountKey.json" >> .gitignore
   echo ".env" >> .gitignore
   ```

2. **Use environment-specific configurations:**
   - Development: Service account key file
   - Production: Environment variables or cloud-native authentication

3. **Implement proper Firestore security rules:**
   - Replace test mode rules with production rules
   - Use role-based access control
   - Validate data on the server side

4. **Monitor usage and costs:**
   - Set up billing alerts in Google Cloud Console
   - Monitor Firestore usage in Firebase Console

## Next Steps

1. **Test all API endpoints:**
   ```bash
   # Test events endpoint
   curl http://localhost:5000/api/events
   
   # Test services endpoint
   curl http://localhost:5000/api/services
   
   # Test creating a booking
   curl -X POST http://localhost:5000/api/bookings \
     -H "Content-Type: application/json" \
     -d '{"eventType":"Birthday","contactEmail":"<EMAIL>",...}'
   ```

2. **Set up your mobile app to use the backend:**
   - Update your mobile app's API base URL to point to your backend
   - Test the integration between mobile app and backend

3. **Deploy to production:**
   - Choose a hosting platform (Heroku, Google Cloud Run, AWS, etc.)
   - Set up production environment variables
   - Deploy your backend server

## Support

If you encounter any issues:

1. Check the [Firebase documentation](https://firebase.google.com/docs)
2. Review the [Node.js Admin SDK guide](https://firebase.google.com/docs/admin/setup)
3. Check the server logs for detailed error messages
4. Ensure your Firebase project has Firestore enabled and properly configured
