{"name": "proto3-json-serializer", "version": "1.1.1", "repository": "googleapis/proto3-json-serializer-nodejs", "description": "Support for proto3 JSON serialiazation/deserialization for protobuf.js", "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src"], "license": "Apache-2.0", "keywords": ["protobufjs", "protobuf.js", "protobuf", "proto3", "json", "serialization", "deserialization"], "scripts": {"test": "c8 node_modules/mocha/bin/mocha build/test/unit", "system-test": "mocha build/test/system", "lint": "gts lint", "clean": "gts clean", "compile": "tsc", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "posttest": "npm run lint", "compile-test-protos": "cd test-fixtures/proto && pbjs -t json test.proto > test.json", "docs": "jsdoc -c .jsdoc.js", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "prelint": "cd samples && npm link ../ && npm install"}, "dependencies": {"protobufjs": "^7.0.0"}, "devDependencies": {"@types/mocha": "^9.0.0", "@types/node": "^18.0.0", "c8": "^7.7.3", "google-proto-files": "^3.0.0", "gts": "^3.1.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^2.0.0", "jsdoc-region-tag": "^2.0.0", "linkinator": "^4.0.0", "mocha": "^9.2.2", "pack-n-play": "^1.0.0-2", "typescript": "^4.6.4"}, "engines": {"node": ">=12.0.0"}}