/**
 * Firebase Firestore Schema Definitions
 * 
 * While Firestore doesn't enforce schemas, these definitions serve as:
 * 1. Documentation for data structure
 * 2. Validation rules for API endpoints
 * 3. Type definitions for development
 * 4. Security rules reference
 */

// Event Schema
const eventSchema = {
  // Document ID: auto-generated or event type (e.g., "Birthday", "Corporate")
  type: {
    type: 'string',
    required: true,
    enum: ['Birthday', 'Corporate', 'HouseWarming', 'Conference'],
    description: 'Type of event'
  },
  metadata: {
    type: 'object',
    required: true,
    properties: {
      title: { type: 'string', required: true },
      description: { type: 'string', required: true },
      images: { 
        type: 'array', 
        items: { type: 'string' }, // URLs
        required: true 
      },
      icon: { type: 'string', required: true },
      path: { type: 'string', required: true }
    }
  },
  questions: {
    type: 'array',
    required: true,
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', required: true },
        text: { type: 'string', required: true },
        type: { 
          type: 'string', 
          required: true,
          enum: ['text', 'select', 'multiselect', 'date', 'time', 'number', 'email', 'phone']
        },
        options: { type: 'array', items: { type: 'string' } }, // for select/multiselect
        placeholder: { type: 'string' },
        required: { type: 'boolean', default: false }
      }
    }
  },
  serviceQuestions: {
    type: 'object',
    description: 'Service-specific questions keyed by service name',
    additionalProperties: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', required: true },
          text: { type: 'string', required: true },
          type: { type: 'string', required: true },
          options: { type: 'array', items: { type: 'string' } },
          placeholder: { type: 'string' },
          required: { type: 'boolean', default: false }
        }
      }
    }
  },
  isStatic: { type: 'boolean', default: false },
  createdAt: { type: 'timestamp', required: true },
  updatedAt: { type: 'timestamp', required: true }
};

// Service Schema
const serviceSchema = {
  // Document ID: auto-generated
  id: { type: 'string', required: true, description: 'Service identifier' },
  title: { type: 'string', required: true },
  description: { type: 'string', required: true },
  longDescription: { type: 'string' },
  icon: { type: 'string', required: true },
  category: { 
    type: 'string',
    enum: ['decoration', 'catering', 'photography', 'videography', 'music', 'transportation', 'other']
  },
  features: {
    type: 'array',
    items: { type: 'string' },
    description: 'List of service features'
  },
  packages: {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        name: { type: 'string', required: true },
        price: { type: 'string', required: true },
        includes: {
          type: 'array',
          items: { type: 'string' },
          required: true
        }
      }
    }
  },
  images: {
    type: 'array',
    items: { type: 'string' }, // URLs
    description: 'Service gallery images'
  },
  rating: { type: 'number', min: 0, max: 5 },
  reviewCount: { type: 'number', min: 0, default: 0 },
  isActive: { type: 'boolean', default: true },
  isStatic: { type: 'boolean', default: false },
  createdAt: { type: 'timestamp', required: true },
  updatedAt: { type: 'timestamp', required: true }
};

// Booking Schema
const bookingSchema = {
  // Document ID: auto-generated
  bookingNumber: { 
    type: 'string', 
    required: true, 
    unique: true,
    description: 'Human-readable booking reference'
  },
  
  // Event Details
  eventType: {
    type: 'string',
    required: true,
    enum: ['Birthday', 'Corporate', 'HouseWarming', 'Conference']
  },
  eventDate: { type: 'string', required: true, format: 'date' }, // ISO date string
  eventTime: { type: 'string', required: true, format: 'time' }, // HH:MM format
  location: { type: 'string', required: true },
  guestCount: { type: 'string', required: true },
  theme: { type: 'string' },
  
  // Services
  services: {
    type: 'array',
    required: true,
    items: { type: 'string' },
    description: 'Selected service names'
  },
  serviceDetails: {
    type: 'object',
    description: 'Service-specific answers keyed by service name',
    additionalProperties: {
      type: 'object',
      description: 'Answers to service questions'
    }
  },
  
  // Contact Information
  contactName: { type: 'string', required: true },
  contactEmail: { type: 'string', required: true, format: 'email' },
  contactPhone: { type: 'string', required: true },
  alternatePhone: { type: 'string' },
  
  // Booking Status
  status: {
    type: 'string',
    required: true,
    enum: ['pending', 'confirmed', 'in-progress', 'completed', 'cancelled'],
    default: 'pending'
  },
  statusNotes: { type: 'string' },
  
  // Pricing
  estimatedCost: { type: 'number', required: true },
  finalCost: { type: 'number' },
  advanceAmount: { type: 'number', default: 0 },
  balanceAmount: { type: 'number', default: 0 },
  
  // Additional Information
  specialRequests: { type: 'string' },
  internalNotes: { type: 'string' },
  
  // Vendor Assignment
  assignedVendors: {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        serviceType: { type: 'string', required: true },
        vendorId: { type: 'string', required: true },
        vendorName: { type: 'string', required: true },
        contactInfo: { type: 'string' },
        assignedAt: { type: 'timestamp', required: true }
      }
    }
  },
  
  // Timestamps
  createdAt: { type: 'timestamp', required: true },
  updatedAt: { type: 'timestamp', required: true },
  confirmedAt: { type: 'timestamp' },
  completedAt: { type: 'timestamp' }
};

// User Schema (for future authentication)
const userSchema = {
  // Document ID: user UID from Firebase Auth
  email: { type: 'string', required: true, format: 'email' },
  displayName: { type: 'string', required: true },
  phoneNumber: { type: 'string' },
  photoURL: { type: 'string' },
  role: {
    type: 'string',
    required: true,
    enum: ['customer', 'vendor', 'admin'],
    default: 'customer'
  },
  
  // Profile Information
  profile: {
    type: 'object',
    properties: {
      firstName: { type: 'string' },
      lastName: { type: 'string' },
      dateOfBirth: { type: 'string', format: 'date' },
      address: {
        type: 'object',
        properties: {
          street: { type: 'string' },
          city: { type: 'string' },
          state: { type: 'string' },
          zipCode: { type: 'string' },
          country: { type: 'string', default: 'India' }
        }
      }
    }
  },
  
  // Preferences
  preferences: {
    type: 'object',
    properties: {
      favoriteEventTypes: { type: 'array', items: { type: 'string' } },
      preferredServices: { type: 'array', items: { type: 'string' } },
      budgetRange: { type: 'string' },
      notifications: {
        type: 'object',
        properties: {
          email: { type: 'boolean', default: true },
          sms: { type: 'boolean', default: true },
          push: { type: 'boolean', default: true }
        }
      }
    }
  },
  
  // Account Status
  isActive: { type: 'boolean', default: true },
  isVerified: { type: 'boolean', default: false },
  lastLoginAt: { type: 'timestamp' },
  createdAt: { type: 'timestamp', required: true },
  updatedAt: { type: 'timestamp', required: true }
};

// Vendor Schema
const vendorSchema = {
  // Document ID: auto-generated
  businessName: { type: 'string', required: true },
  ownerName: { type: 'string', required: true },
  email: { type: 'string', required: true, format: 'email' },
  phone: { type: 'string', required: true },
  alternatePhone: { type: 'string' },
  
  // Business Information
  services: {
    type: 'array',
    required: true,
    items: { type: 'string' },
    description: 'Services offered by vendor'
  },
  serviceAreas: {
    type: 'array',
    required: true,
    items: { type: 'string' },
    description: 'Geographic areas served'
  },
  businessAddress: {
    type: 'object',
    required: true,
    properties: {
      street: { type: 'string', required: true },
      city: { type: 'string', required: true },
      state: { type: 'string', required: true },
      zipCode: { type: 'string', required: true },
      country: { type: 'string', default: 'India' }
    }
  },
  
  // Business Details
  description: { type: 'string' },
  experience: { type: 'number', description: 'Years of experience' },
  teamSize: { type: 'number' },
  portfolio: {
    type: 'array',
    items: { type: 'string' }, // URLs to portfolio images
    description: 'Portfolio images'
  },
  
  // Ratings and Reviews
  rating: { type: 'number', min: 0, max: 5, default: 0 },
  reviewCount: { type: 'number', min: 0, default: 0 },
  completedBookings: { type: 'number', min: 0, default: 0 },
  
  // Verification and Status
  isVerified: { type: 'boolean', default: false },
  isActive: { type: 'boolean', default: true },
  verificationDocuments: {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        type: { type: 'string', required: true }, // 'license', 'insurance', 'tax'
        url: { type: 'string', required: true },
        expiryDate: { type: 'string', format: 'date' },
        verified: { type: 'boolean', default: false }
      }
    }
  },
  
  // Timestamps
  createdAt: { type: 'timestamp', required: true },
  updatedAt: { type: 'timestamp', required: true },
  verifiedAt: { type: 'timestamp' }
};

// Review Schema
const reviewSchema = {
  // Document ID: auto-generated
  bookingId: { type: 'string', required: true },
  userId: { type: 'string', required: true },
  vendorId: { type: 'string', required: true },
  serviceType: { type: 'string', required: true },
  
  // Review Content
  rating: { type: 'number', required: true, min: 1, max: 5 },
  title: { type: 'string', required: true },
  comment: { type: 'string', required: true },
  images: {
    type: 'array',
    items: { type: 'string' }, // URLs
    description: 'Review images'
  },
  
  // Review Status
  isVerified: { type: 'boolean', default: false },
  isPublic: { type: 'boolean', default: true },
  
  // Timestamps
  createdAt: { type: 'timestamp', required: true },
  updatedAt: { type: 'timestamp', required: true }
};

module.exports = {
  eventSchema,
  serviceSchema,
  bookingSchema,
  userSchema,
  vendorSchema,
  reviewSchema
};
