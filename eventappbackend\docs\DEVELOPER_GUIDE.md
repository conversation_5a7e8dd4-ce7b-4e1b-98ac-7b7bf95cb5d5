# Developer Guide

Welcome to the Event App Backend! This guide will help you understand the codebase structure, architecture, and development workflow.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Getting Started](#getting-started)
4. [Code Structure](#code-structure)
5. [Development Workflow](#development-workflow)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Contributing](#contributing)

## Project Overview

The Event App Backend is a Node.js REST API server that powers an event management mobile application. It provides:

- **Event Management**: Different event types (Birthday, Corporate, HouseWarming, Conference)
- **Service Catalog**: Various services like decoration, catering, photography
- **Booking System**: Complete booking lifecycle management
- **User Management**: Customer and vendor profiles
- **Review System**: Customer feedback and ratings

### Tech Stack

- **Runtime**: Node.js 16+
- **Framework**: Express.js
- **Database**: Firebase Firestore (NoSQL)
- **Authentication**: Firebase Admin SDK
- **Validation**: express-validator
- **Testing**: Jest + Supertest
- **Documentation**: Markdown

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Web App       │    │   Admin Panel   │
│   (React Native)│    │   (React)       │    │   (React)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Express.js API       │
                    │   (Node.js Backend)      │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────▼─────────────┐
                    │   Firebase Firestore     │
                    │     (Database)           │
                    └───────────────────────────┘
```

### API Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        Express.js App                       │
├─────────────────────────────────────────────────────────────┤
│  Middleware Layer                                           │
│  ├─ Security (Helmet, CORS, Rate Limiting)                 │
│  ├─ Validation (express-validator)                         │
│  ├─ Logging (Morgan)                                       │
│  └─ Error Handling                                         │
├─────────────────────────────────────────────────────────────┤
│  Route Layer                                               │
│  ├─ /api/health    (Health checks)                        │
│  ├─ /api/events    (Event management)                     │
│  ├─ /api/services  (Service catalog)                      │
│  └─ /api/bookings  (Booking system)                       │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer                                      │
│  ├─ Schema Validation                                      │
│  ├─ Data Transformation                                    │
│  └─ Business Rules                                         │
├─────────────────────────────────────────────────────────────┤
│  Data Access Layer                                         │
│  ├─ Firebase Helpers                                       │
│  ├─ Collection Management                                  │
│  └─ Query Optimization                                     │
└─────────────────────────────────────────────────────────────┘
```

## Getting Started

### Prerequisites

- Node.js 16+ and npm
- Firebase project with Firestore enabled
- Git for version control
- Code editor (VS Code recommended)

### Quick Setup

1. **Clone and install:**
   ```bash
   git clone <repository-url>
   cd eventappbackend
   npm install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your Firebase credentials
   ```

3. **Test Firebase connection:**
   ```bash
   npm run test-firebase
   ```

4. **Seed initial data:**
   ```bash
   npm run seed-data
   ```

5. **Start development server:**
   ```bash
   npm run dev
   ```

### Development Environment

- **Port**: 5000 (configurable via PORT env var)
- **Hot Reload**: Enabled via nodemon
- **Logging**: Console output with request details
- **CORS**: Configured for local development

## Code Structure

### Directory Structure

```
eventappbackend/
├── config/                 # Configuration files
│   └── firebase.js         # Firebase setup and helpers
├── docs/                   # Documentation
│   ├── DEVELOPER_GUIDE.md  # This file
│   ├── API_REFERENCE.md    # API documentation
│   ├── SCHEMA.md          # Database schema
│   └── FIREBASE_SETUP.md  # Firebase setup guide
├── middleware/             # Express middleware
│   ├── errorHandler.js    # Global error handling
│   ├── notFound.js        # 404 handler
│   └── validation.js      # Input validation rules
├── models/                 # Data models and schemas
│   └── schemas.js         # Firestore schema definitions
├── routes/                 # API route handlers
│   ├── health.js          # Health check endpoints
│   ├── events.js          # Event management
│   ├── services.js        # Service catalog
│   └── bookings.js        # Booking system
├── scripts/                # Utility scripts
│   ├── initDatabase.js    # Database initialization
│   ├── seedData.js        # Data seeding
│   └── testFirebase.js    # Firebase connection test
├── tests/                  # Test files
│   └── health.test.js     # Health endpoint tests
├── utils/                  # Utility functions
│   └── schemaValidator.js # Schema validation utility
├── .env                    # Environment variables
├── .gitignore             # Git ignore rules
├── package.json           # Dependencies and scripts
├── server.js              # Main application entry point
└── README.md              # Project overview
```

### Key Files Explained

#### `server.js` - Application Entry Point
- Initializes Express app
- Sets up middleware stack
- Defines routes
- Starts the server

#### `config/firebase.js` - Firebase Configuration
- Firebase Admin SDK initialization
- Firestore connection management
- Helper functions for database operations
- Collection references and constants

#### `routes/*.js` - API Route Handlers
- RESTful endpoint implementations
- Request validation
- Business logic
- Response formatting

#### `middleware/` - Express Middleware
- **errorHandler.js**: Centralized error handling
- **validation.js**: Input validation rules using express-validator
- **notFound.js**: 404 error handler

#### `models/schemas.js` - Data Models
- Firestore collection schemas
- Validation rules
- Data structure definitions

#### `utils/schemaValidator.js` - Validation Utility
- Schema validation logic
- Data sanitization
- Error formatting

## Development Workflow

### Adding New Features

1. **Plan the Feature**
   - Define requirements
   - Design API endpoints
   - Plan database schema changes

2. **Create/Update Schema**
   ```javascript
   // In models/schemas.js
   const newFeatureSchema = {
     field1: { type: 'string', required: true },
     field2: { type: 'number', min: 0 }
   };
   ```

3. **Add Validation Rules**
   ```javascript
   // In middleware/validation.js
   const newFeatureValidation = [
     body('field1').isString().notEmpty(),
     body('field2').isNumeric().isInt({ min: 0 }),
     handleValidationErrors
   ];
   ```

4. **Create Route Handler**
   ```javascript
   // In routes/newFeature.js
   router.post('/', newFeatureValidation, async (req, res) => {
     try {
       // Validate schema
       const validation = SchemaValidator.validateNewFeature(req.body);
       if (!validation.isValid) {
         return res.status(400).json({
           success: false,
           error: { message: 'Validation failed', details: validation.errors }
         });
       }
       
       // Business logic
       const result = await firestoreHelpers.addDocument('collection', req.body);
       
       res.status(201).json({
         success: true,
         data: result
       });
     } catch (error) {
       res.status(500).json({
         success: false,
         error: { message: 'Operation failed', details: error.message }
       });
     }
   });
   ```

5. **Add Route to Server**
   ```javascript
   // In server.js
   const newFeatureRoutes = require('./routes/newFeature');
   app.use('/api/new-feature', newFeatureRoutes);
   ```

6. **Write Tests**
   ```javascript
   // In tests/newFeature.test.js
   describe('New Feature API', () => {
     it('should create new feature', async () => {
       const response = await request(app)
         .post('/api/new-feature')
         .send(validData)
         .expect(201);
       
       expect(response.body.success).toBe(true);
     });
   });
   ```

### Code Style Guidelines

#### Naming Conventions
- **Files**: camelCase (e.g., `userController.js`)
- **Functions**: camelCase (e.g., `getUserById`)
- **Variables**: camelCase (e.g., `userData`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_RETRY_ATTEMPTS`)
- **Collections**: lowercase (e.g., `users`, `bookings`)

#### Error Handling
```javascript
// Always use try-catch for async operations
try {
  const result = await someAsyncOperation();
  res.json({ success: true, data: result });
} catch (error) {
  console.error('Operation failed:', error);
  res.status(500).json({
    success: false,
    error: {
      message: 'User-friendly error message',
      details: error.message
    }
  });
}
```

#### Response Format
```javascript
// Success Response
{
  "success": true,
  "data": {...},
  "count": 10,        // For list responses
  "total": 100,       // For paginated responses
  "message": "..."    // Optional success message
}

// Error Response
{
  "success": false,
  "error": {
    "message": "User-friendly error message",
    "details": "Technical error details",
    "code": "ERROR_CODE"  // Optional error code
  }
}
```

### Database Operations

#### Using Firestore Helpers
```javascript
const { firestoreHelpers, collections } = require('../config/firebase');

// Create document
const docId = await firestoreHelpers.addDocument(collections.USERS, userData);

// Get document by ID
const user = await firestoreHelpers.getDocument(collections.USERS, userId);

// Query documents
const users = await firestoreHelpers.queryDocuments(
  collections.USERS,
  [{ field: 'role', operator: '==', value: 'customer' }],
  10, // limit
  { field: 'createdAt', direction: 'desc' } // orderBy
);

// Update document
await firestoreHelpers.updateDocument(collections.USERS, userId, updateData);

// Delete document
await firestoreHelpers.deleteDocument(collections.USERS, userId);
```

#### Schema Validation
```javascript
const SchemaValidator = require('../utils/schemaValidator');

// Validate data before saving
const validation = SchemaValidator.validateUser(userData);
if (!validation.isValid) {
  return res.status(400).json({
    success: false,
    error: {
      message: 'Validation failed',
      details: validation.errors
    }
  });
}

// Sanitize data
const sanitizedData = SchemaValidator.sanitize(userData, userSchema);
```

## Testing

### Test Structure
```
tests/
├── unit/           # Unit tests for individual functions
├── integration/    # Integration tests for API endpoints
└── fixtures/       # Test data and mocks
```

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Writing Tests
```javascript
const request = require('supertest');
const app = require('../server');

describe('API Endpoint', () => {
  beforeEach(() => {
    // Setup test data
  });

  afterEach(() => {
    // Cleanup
  });

  it('should handle valid request', async () => {
    const response = await request(app)
      .post('/api/endpoint')
      .send(validData)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data).toHaveProperty('id');
  });

  it('should handle invalid request', async () => {
    const response = await request(app)
      .post('/api/endpoint')
      .send(invalidData)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error).toHaveProperty('message');
  });
});
```

## Deployment

### Environment Configuration
```bash
# Production environment variables
NODE_ENV=production
PORT=5000
FIREBASE_PROJECT_ID=your-production-project
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}
ALLOWED_ORIGINS=https://your-production-domain.com
```

### Deployment Platforms

#### Heroku
```bash
# Install Heroku CLI and login
heroku create your-app-name
heroku config:set NODE_ENV=production
heroku config:set FIREBASE_PROJECT_ID=your-project-id
# ... set other environment variables
git push heroku main
```

#### Google Cloud Run
```bash
# Build and deploy
gcloud run deploy --source .
```

#### AWS EC2/ECS
- Use Docker container
- Set up load balancer
- Configure auto-scaling

### Pre-deployment Checklist
- [ ] Environment variables configured
- [ ] Firebase security rules deployed
- [ ] Database indexes created
- [ ] Tests passing
- [ ] Error monitoring set up
- [ ] Logging configured
- [ ] Performance monitoring enabled

## Contributing

### Pull Request Process
1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Make changes and add tests
4. Ensure all tests pass: `npm test`
5. Update documentation if needed
6. Commit changes: `git commit -m "Add new feature"`
7. Push to branch: `git push origin feature/new-feature`
8. Create Pull Request

### Code Review Guidelines
- Code follows style guidelines
- Tests are included and passing
- Documentation is updated
- No security vulnerabilities
- Performance considerations addressed

### Issue Reporting
When reporting issues, include:
- Steps to reproduce
- Expected vs actual behavior
- Environment details
- Error messages/logs
- Screenshots if applicable

## Additional Resources

- [Express.js Documentation](https://expressjs.com/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)
- [REST API Design Guidelines](https://restfulapi.net/)

## Getting Help

- Check existing documentation in `/docs`
- Search through existing issues
- Ask questions in team chat
- Schedule code review sessions
- Pair programming for complex features

Remember: Good code is not just working code, but code that is readable, maintainable, and well-documented!
