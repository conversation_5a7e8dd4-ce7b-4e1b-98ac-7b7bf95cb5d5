/**
 * Firebase Connection Test Script
 * 
 * This script tests the Firebase connection and helps diagnose authentication issues.
 */

require('dotenv').config();

async function testFirebaseConnection() {
  console.log('🔥 Testing Firebase connection...\n');
  
  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log(`- NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
  console.log(`- FIREBASE_PROJECT_ID: ${process.env.FIREBASE_PROJECT_ID || 'not set'}`);
  console.log(`- GOOGLE_APPLICATION_CREDENTIALS: ${process.env.GOOGLE_APPLICATION_CREDENTIALS || 'not set'}`);
  console.log(`- FIREBASE_SERVICE_ACCOUNT_KEY: ${process.env.FIREBASE_SERVICE_ACCOUNT_KEY ? 'set (hidden)' : 'not set'}`);
  console.log('');

  // Check if required environment variables are set
  if (!process.env.FIREBASE_PROJECT_ID) {
    console.error('❌ FIREBASE_PROJECT_ID is not set in environment variables');
    console.log('💡 Please set FIREBASE_PROJECT_ID in your .env file');
    return;
  }

  // Check authentication method
  let authMethod = 'none';
  if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
    authMethod = 'service_account_key_env';
  } else if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    authMethod = 'service_account_key_file';
  } else {
    authMethod = 'application_default_credentials';
  }
  
  console.log(`🔐 Authentication method: ${authMethod}`);
  console.log('');

  // Test service account key file if specified
  if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    const fs = require('fs');
    const path = require('path');
    
    try {
      const keyPath = path.resolve(process.env.GOOGLE_APPLICATION_CREDENTIALS);
      console.log(`📁 Checking service account key file: ${keyPath}`);
      
      if (!fs.existsSync(keyPath)) {
        console.error('❌ Service account key file does not exist');
        console.log('💡 Please ensure the file path is correct and the file exists');
        return;
      }
      
      const keyContent = fs.readFileSync(keyPath, 'utf8');
      const keyData = JSON.parse(keyContent);
      
      console.log(`✅ Service account key file found`);
      console.log(`- Project ID: ${keyData.project_id}`);
      console.log(`- Client Email: ${keyData.client_email}`);
      console.log(`- Key ID: ${keyData.private_key_id?.substring(0, 8)}...`);
      
      if (keyData.project_id !== process.env.FIREBASE_PROJECT_ID) {
        console.warn('⚠️  Project ID in service account key does not match FIREBASE_PROJECT_ID');
      }
      
    } catch (error) {
      console.error('❌ Error reading service account key file:', error.message);
      return;
    }
  }

  // Test service account key environment variable if specified
  if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
    try {
      const keyData = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
      console.log(`✅ Service account key environment variable found`);
      console.log(`- Project ID: ${keyData.project_id}`);
      console.log(`- Client Email: ${keyData.client_email}`);
      
      if (keyData.project_id !== process.env.FIREBASE_PROJECT_ID) {
        console.warn('⚠️  Project ID in service account key does not match FIREBASE_PROJECT_ID');
      }
      
    } catch (error) {
      console.error('❌ Error parsing service account key from environment variable:', error.message);
      return;
    }
  }

  console.log('');

  // Test Firebase initialization
  try {
    console.log('🚀 Initializing Firebase...');
    const { initializeFirebase, getDb } = require('../config/firebase');
    
    const db = initializeFirebase();
    console.log('✅ Firebase initialized successfully');
    
    // Test database connection
    console.log('🔍 Testing database connection...');
    const testCollection = db.collection('health');
    await testCollection.add({
      test: true,
      timestamp: new Date(),
      message: 'Firebase connection test'
    });
    
    console.log('✅ Database write test successful');
    
    // Test database read
    const snapshot = await testCollection.limit(1).get();
    console.log('✅ Database read test successful');
    console.log(`📊 Test documents found: ${snapshot.size}`);
    
    // Clean up test document
    if (!snapshot.empty) {
      await snapshot.docs[0].ref.delete();
      console.log('🧹 Test document cleaned up');
    }
    
    console.log('\n🎉 All Firebase tests passed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Run: npm run init-db');
    console.log('2. Run: npm run dev');
    console.log('3. Test: curl http://localhost:5000/api/health');
    
  } catch (error) {
    console.error('\n❌ Firebase connection test failed:', error.message);
    console.log('\n🔧 Troubleshooting steps:');
    
    if (error.message.includes('default credentials')) {
      console.log('1. Ensure you have set up Firebase credentials properly');
      console.log('2. Check the Firebase Setup Guide: docs/FIREBASE_SETUP.md');
      console.log('3. Verify your .env file has the correct Firebase configuration');
    }
    
    if (error.message.includes('permission')) {
      console.log('1. Check your Firestore security rules');
      console.log('2. Ensure your service account has the necessary permissions');
      console.log('3. Verify your project ID is correct');
    }
    
    if (error.message.includes('not found')) {
      console.log('1. Verify your Firebase project ID is correct');
      console.log('2. Ensure Firestore is enabled in your Firebase project');
      console.log('3. Check that your project exists in Firebase Console');
    }
    
    console.log('\n📚 Resources:');
    console.log('- Firebase Setup Guide: docs/FIREBASE_SETUP.md');
    console.log('- Firebase Console: https://console.firebase.google.com/');
    console.log('- Firebase Documentation: https://firebase.google.com/docs');
  }
}

// Run the test
if (require.main === module) {
  testFirebaseConnection()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test script failed:', error);
      process.exit(1);
    });
}

module.exports = testFirebaseConnection;
