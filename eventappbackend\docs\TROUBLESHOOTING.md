# Troubleshooting Guide

This guide helps developers diagnose and resolve common issues with the Event App Backend.

## Table of Contents

1. [Common Issues](#common-issues)
2. [Firebase Issues](#firebase-issues)
3. [API Issues](#api-issues)
4. [Performance Issues](#performance-issues)
5. [Deployment Issues](#deployment-issues)
6. [Development Issues](#development-issues)
7. [Debugging Tools](#debugging-tools)
8. [Getting Help](#getting-help)

## Common Issues

### Server Won't Start

#### Issue: Port Already in Use
```
Error: listen EADDRINUSE: address already in use :::5000
```

**Solutions:**
```bash
# Find process using port 5000
lsof -i :5000
# or on Windows
netstat -ano | findstr :5000

# Kill the process
kill -9 <PID>
# or on Windows
taskkill /PID <PID> /F

# Or use a different port
PORT=5001 npm run dev
```

#### Issue: Missing Dependencies
```
Error: Cannot find module 'express'
```

**Solutions:**
```bash
# Install dependencies
npm install

# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version  # Should be 16+
```

#### Issue: Environment Variables Not Loaded
```
Error: FIREBASE_PROJECT_ID is not defined
```

**Solutions:**
```bash
# Check .env file exists
ls -la .env

# Verify .env content
cat .env | grep FIREBASE_PROJECT_ID

# Copy from example
cp .env.example .env
# Edit .env with your values

# Check if dotenv is loaded
node -e "require('dotenv').config(); console.log(process.env.FIREBASE_PROJECT_ID)"
```

### Database Connection Issues

#### Issue: Firebase Authentication Failed
```
Error: Could not load the default credentials
```

**Solutions:**
```bash
# Test Firebase connection
npm run test-firebase

# Check service account key format
node -e "console.log(JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY))"

# Verify project ID matches
echo $FIREBASE_PROJECT_ID

# Check Firebase project exists
firebase projects:list
```

#### Issue: Firestore Permission Denied
```
Error: 7 PERMISSION_DENIED: Missing or insufficient permissions
```

**Solutions:**
```bash
# Check Firestore security rules
firebase firestore:rules:get

# Deploy test rules (temporary)
firebase deploy --only firestore:rules

# Verify service account permissions in Firebase Console
# IAM & Admin > Service Accounts > Check roles
```

#### Issue: Firestore Not Found
```
Error: 5 NOT_FOUND: Project not found
```

**Solutions:**
```bash
# Verify project ID
firebase projects:list

# Check if Firestore is enabled
# Go to Firebase Console > Firestore Database

# Create Firestore database
firebase firestore:databases:create --location=us-central1
```

## Firebase Issues

### Authentication Issues

#### Issue: Invalid Service Account Key
```
Error: Error parsing service account key
```

**Diagnosis:**
```bash
# Validate JSON format
echo $FIREBASE_SERVICE_ACCOUNT_KEY | jq .

# Check required fields
echo $FIREBASE_SERVICE_ACCOUNT_KEY | jq '.project_id, .client_email, .private_key'
```

**Solutions:**
1. Re-download service account key from Firebase Console
2. Ensure JSON is properly escaped in environment variable
3. Use file-based credentials instead:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS=./config/serviceAccountKey.json
   ```

#### Issue: Project ID Mismatch
```
Error: Project ID in credentials doesn't match environment
```

**Solutions:**
```bash
# Check project IDs match
echo "Env: $FIREBASE_PROJECT_ID"
echo "Key: $(echo $FIREBASE_SERVICE_ACCOUNT_KEY | jq -r .project_id)"

# Update environment variable
export FIREBASE_PROJECT_ID=correct-project-id
```

### Database Operation Issues

#### Issue: Document Not Found
```
Error: No document to update
```

**Diagnosis:**
```bash
# Check if document exists
curl http://localhost:5000/api/bookings/document-id

# List all documents in collection
curl http://localhost:5000/api/bookings
```

**Solutions:**
1. Verify document ID is correct
2. Check if document was deleted
3. Ensure proper error handling for missing documents

#### Issue: Query Performance Slow
```
Warning: Query requires index
```

**Solutions:**
```bash
# Create required indexes
firebase firestore:indexes:create --collection-group=bookings --query-scope=COLLECTION --fields="status:asc,createdAt:desc"

# Check existing indexes
firebase firestore:indexes:list

# Monitor query performance in Firebase Console
```

## API Issues

### Request/Response Issues

#### Issue: CORS Errors
```
Error: Access to fetch blocked by CORS policy
```

**Diagnosis:**
```bash
# Check CORS configuration
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     http://localhost:5000/api/bookings
```

**Solutions:**
```javascript
// Update CORS configuration in server.js
app.use(cors({
  origin: ['http://localhost:3000', 'https://your-domain.com'],
  credentials: true
}));
```

#### Issue: Validation Errors
```
Error: Validation failed
```

**Diagnosis:**
```bash
# Test with valid data
curl -X POST http://localhost:5000/api/bookings \
  -H "Content-Type: application/json" \
  -d '{
    "eventType": "Birthday",
    "eventDate": "2024-12-25",
    "eventTime": "18:00",
    "location": "Test Venue",
    "guestCount": "21-50 guests",
    "services": ["Decoration"],
    "contactName": "Test User",
    "contactEmail": "<EMAIL>",
    "contactPhone": "9876543210"
  }'
```

**Solutions:**
1. Check validation rules in `middleware/validation.js`
2. Verify request data format
3. Review schema definitions in `models/schemas.js`

#### Issue: Rate Limiting
```
Error: Too many requests from this IP
```

**Solutions:**
```bash
# Check rate limit configuration
grep -r "rateLimit" server.js

# Temporarily disable for testing
# Comment out rate limiting middleware

# Increase limits for development
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 1000 // Increase limit
});
```

### Authentication Issues

#### Issue: Unauthorized Access
```
Error: 401 Unauthorized
```

**Solutions:**
1. Implement proper authentication middleware
2. Check JWT token validation
3. Verify API key configuration

## Performance Issues

### Slow Response Times

#### Issue: Database Queries Slow
**Diagnosis:**
```bash
# Monitor response times
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:5000/api/events"

# Create curl-format.txt:
echo "     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n" > curl-format.txt
```

**Solutions:**
1. Add database indexes
2. Implement pagination
3. Use query optimization
4. Add caching layer

#### Issue: Memory Leaks
**Diagnosis:**
```bash
# Monitor memory usage
node --inspect server.js
# Open Chrome DevTools > Memory tab

# Or use PM2
pm2 monit
```

**Solutions:**
1. Check for unclosed database connections
2. Review event listeners for memory leaks
3. Implement proper garbage collection
4. Use memory profiling tools

### High CPU Usage

**Diagnosis:**
```bash
# Monitor CPU usage
top -p $(pgrep node)

# Profile with Node.js
node --prof server.js
# Generate report
node --prof-process isolate-*.log > processed.txt
```

**Solutions:**
1. Optimize synchronous operations
2. Use clustering for CPU-intensive tasks
3. Implement caching
4. Review algorithm efficiency

## Deployment Issues

### Build Failures

#### Issue: Build Process Fails
```
Error: Build failed with exit code 1
```

**Diagnosis:**
```bash
# Check build logs
npm run build 2>&1 | tee build.log

# Verify dependencies
npm ls

# Check for missing environment variables
env | grep -E "(NODE_ENV|FIREBASE)"
```

**Solutions:**
1. Ensure all dependencies are installed
2. Set proper environment variables
3. Check Node.js version compatibility
4. Review build scripts in package.json

### Container Issues

#### Issue: Docker Build Fails
```
Error: Docker build failed
```

**Diagnosis:**
```bash
# Build with verbose output
docker build --no-cache --progress=plain .

# Check Dockerfile syntax
docker build --dry-run .
```

**Solutions:**
1. Update base image version
2. Check file permissions
3. Verify COPY paths
4. Review multi-stage build configuration

### SSL/TLS Issues

#### Issue: SSL Certificate Problems
```
Error: SSL certificate verification failed
```

**Solutions:**
```bash
# Check certificate status
openssl s_client -connect your-domain.com:443

# Verify certificate chain
curl -vI https://your-domain.com

# Renew Let's Encrypt certificate
sudo certbot renew
```

## Development Issues

### Hot Reload Not Working

#### Issue: Nodemon Not Restarting
**Solutions:**
```bash
# Check nodemon configuration
cat nodemon.json

# Restart with verbose logging
npx nodemon --verbose server.js

# Clear nodemon cache
npx nodemon --clear-cache
```

### IDE Issues

#### Issue: IntelliSense Not Working
**Solutions:**
1. Install proper TypeScript definitions:
   ```bash
   npm install --save-dev @types/node @types/express
   ```
2. Configure VS Code settings
3. Restart language server

#### Issue: Debugging Not Working
**Solutions:**
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Node.js",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/server.js",
      "env": {
        "NODE_ENV": "development"
      },
      "envFile": "${workspaceFolder}/.env"
    }
  ]
}
```

## Debugging Tools

### Logging and Monitoring

#### Enable Debug Logging
```bash
# Enable debug mode
DEBUG=* npm run dev

# Specific debug namespace
DEBUG=express:* npm run dev

# Custom debug logging
const debug = require('debug')('app:database');
debug('Database operation completed');
```

#### Request Logging
```javascript
// Enhanced request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`, {
    headers: req.headers,
    body: req.body,
    query: req.query,
    params: req.params
  });
  next();
});
```

### Performance Profiling

#### CPU Profiling
```bash
# Start with profiling
node --prof server.js

# Generate report after stopping
node --prof-process isolate-*.log > profile.txt
```

#### Memory Profiling
```bash
# Heap snapshot
node --inspect server.js
# Open chrome://inspect in Chrome
# Take heap snapshots in DevTools
```

### Network Debugging

#### Request/Response Debugging
```bash
# Verbose curl
curl -v http://localhost:5000/api/health

# With timing
curl -w "@curl-format.txt" http://localhost:5000/api/health

# Test with different methods
curl -X POST -H "Content-Type: application/json" -d '{}' http://localhost:5000/api/bookings
```

#### Network Connectivity
```bash
# Test DNS resolution
nslookup your-domain.com

# Test port connectivity
telnet your-domain.com 443

# Check firewall rules
sudo ufw status
```

## Getting Help

### Information to Gather

When seeking help, provide:

1. **Error Messages**: Complete error stack traces
2. **Environment**: Node.js version, OS, deployment platform
3. **Configuration**: Relevant environment variables (redacted)
4. **Steps to Reproduce**: Exact steps that cause the issue
5. **Expected vs Actual**: What should happen vs what actually happens

### Useful Commands for Diagnostics

```bash
# System information
node --version
npm --version
uname -a

# Application status
pm2 status
pm2 logs

# Network status
netstat -tulpn | grep :5000
ss -tulpn | grep :5000

# Disk space
df -h

# Memory usage
free -h

# Process information
ps aux | grep node
```

### Log Collection

```bash
# Collect all relevant logs
mkdir debug-logs
cp .env.example debug-logs/  # Don't include actual .env
npm run test > debug-logs/test-output.log 2>&1
pm2 logs > debug-logs/pm2-logs.log
journalctl -u nginx > debug-logs/nginx-logs.log
tar -czf debug-logs.tar.gz debug-logs/
```

### Community Resources

- **GitHub Issues**: Check existing issues and create new ones
- **Stack Overflow**: Search for similar problems
- **Firebase Documentation**: Official Firebase guides
- **Express.js Documentation**: Framework-specific help
- **Node.js Documentation**: Runtime-specific issues

### Emergency Procedures

#### Service Down
1. Check health endpoint: `curl http://your-domain.com/api/health`
2. Restart application: `pm2 restart eventapp-backend`
3. Check logs: `pm2 logs eventapp-backend`
4. Verify database connectivity: `npm run test-firebase`
5. Check system resources: `htop`, `df -h`

#### Data Loss Prevention
1. Immediate backup: Use Firebase export
2. Stop write operations if possible
3. Investigate cause before restoration
4. Document incident for post-mortem

#### Security Incident
1. Rotate all credentials immediately
2. Check access logs for unauthorized access
3. Update security rules
4. Monitor for suspicious activity
5. Document and report incident

Remember: When in doubt, check the logs first, then verify configuration, and finally test with minimal examples to isolate the issue.
