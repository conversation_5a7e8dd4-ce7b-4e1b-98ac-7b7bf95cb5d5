import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, ActivityIndicator, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { Service } from '../../types/service';
import { servicesApi } from '../../lib/api/services';

export default function ServicesPage() {
  const router = useRouter();
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await servicesApi.getAll();

      if (response.success && response.data) {
        setServices(response.data);
      } else {
        setError(response.error?.message || 'Failed to fetch services');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleServicePress = (serviceId: string) => {
    router.push({
      pathname: '/service/[id]',
      params: { id: serviceId }
    });
  };

  const handleRetry = () => {
    fetchServices();
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#FF3366" />
        <Text style={styles.loadingText}>Loading services...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.scrollView}>
      <View style={styles.grid}>
        {services.length > 0 ? (
          services.map((service) => (
            <TouchableOpacity
              key={service.id}
              style={styles.card}
              onPress={() => handleServicePress(service.id)}
              activeOpacity={0.85}
            >
              <View style={styles.imageContainer}>
                <Image
                  source={{ uri: service.image || 'https://via.placeholder.com/400x300?text=Service+Image' }}
                  style={styles.cardImage}
                  resizeMode="cover"
                />
              </View>
              <Text style={styles.icon}>{service.icon}</Text>
              <Text style={styles.title}>{service.title}</Text>
              <Text style={styles.description} numberOfLines={2}>
                {service.description}
              </Text>
              <View style={styles.features}>
                {service.features?.slice(0, 2).map((feature: string, i: number) => (
                  <Text key={i} style={styles.featureItem}>• {feature}</Text>
                ))}
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <Text style={styles.errorText}>No services available</Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#fff',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 8,
  },
  card: {
    width: '45%',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 0,
    margin: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  imageContainer: {
    width: '100%',
    aspectRatio: 1.2,
    backgroundColor: '#f0f0f0',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  icon: {
    fontSize: 28,
    marginTop: -18,
    alignSelf: 'flex-end',
    marginRight: 12,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 4,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#222',
    marginLeft: 12,
    marginTop: 4,
  },
  description: {
    fontSize: 13,
    color: '#666',
    marginBottom: 8,
    marginLeft: 12,
    marginRight: 12,
  },
  features: {
    marginTop: 2,
    marginLeft: 12,
    marginBottom: 8,
  },
  featureItem: {
    fontSize: 12,
    color: '#555',
    marginBottom: 2,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#FF3366',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 15,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});