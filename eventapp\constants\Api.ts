// API Configuration
export const API_CONFIG = {
  BASE_URL: __DEV__ 
    ? 'http://localhost:5000/api'  // Development URL
    : 'https://your-production-api.com/api', // Production URL
  ENDPOINTS: {
    SERVICES: '/services',
    EVENTS: '/events',
    BOOKINGS: '/bookings',
  },
  TIMEOUT: 10000, // 10 seconds
};

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  count?: number;
  total?: number;
  error?: {
    message: string;
    details?: string;
  };
}

// API Helper Functions
export const createApiUrl = (endpoint: string, params?: Record<string, string | number>): string => {
  const url = new URL(API_CONFIG.BASE_URL + endpoint);
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value.toString());
    });
  }
  
  return url.toString();
};

export const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  params?: Record<string, string | number>
): Promise<ApiResponse<T>> => {
  const url = createApiUrl(endpoint, params);
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: API_CONFIG.TIMEOUT,
  };

  const requestOptions = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, requestOptions);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error?.message || `HTTP error! status: ${response.status}`);
    }
    
    return data;
  } catch (error) {
    console.error('API request failed:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      },
    };
  }
};
