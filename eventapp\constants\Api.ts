// API Configuration
const getApiBaseUrl = () => {
  if (!__DEV__) {
    return 'https://your-production-api.com/api'; // Production URL
  }

  // Development URLs - try different options based on platform
  if (typeof window !== 'undefined' && window.location) {
    // Web environment - use localhost
    return 'http://localhost:5000/api';
  } else {
    // Mobile environment - use local IP
    return 'http://*************:5000/api';
  }
};

export const API_CONFIG = {
  BASE_URL: getApiBaseUrl(),
  ENDPOINTS: {
    SERVICES: '/services',
    EVENTS: '/events',
    BOOKINGS: '/bookings',
  },
  TIMEOUT: 10000, // 10 seconds
};

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  count?: number;
  total?: number;
  error?: {
    message: string;
    details?: string;
  };
}

// API Helper Functions
export const createApiUrl = (endpoint: string, params?: Record<string, string | number>): string => {
  const url = new URL(API_CONFIG.BASE_URL + endpoint);
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value.toString());
    });
  }
  
  return url.toString();
};

export const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  params?: Record<string, string | number>
): Promise<ApiResponse<T>> => {
  const url = createApiUrl(endpoint, params);

  console.log('Making API request to:', url);

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: API_CONFIG.TIMEOUT,
  };

  const requestOptions = { ...defaultOptions, ...options };

  console.log('Request options:', requestOptions);

  try {
    console.log('Fetching data from:', url);
    const response = await fetch(url, requestOptions);
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);

    const data = await response.json();
    console.log('Response data:', data);

    if (!response.ok) {
      throw new Error(data.error?.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('API request failed:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      url,
      endpoint,
      params
    });

    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      },
    };
  }
};
