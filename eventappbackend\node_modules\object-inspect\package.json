{"name": "object-inspect", "version": "1.13.4", "description": "string representations of objects in node and the browser", "main": "index.js", "sideEffects": false, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@pkgjs/support": "^0.0.6", "auto-changelog": "^2.5.0", "core-js": "^2.6.12", "error-cause": "^1.0.8", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "for-each": "^0.3.4", "functions-have-names": "^1.2.3", "glob": "=10.3.7", "globalthis": "^1.0.4", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "make-arrow-function": "^1.2.0", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "safer-buffer": "^2.1.2", "semver": "^6.3.1", "string.prototype.repeat": "^1.0.0", "tape": "^5.9.0"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "postlint": "npx @pkgjs/support validate", "test": "npm run tests-only && npm run test:corejs", "tests-only": "nyc tape 'test/*.js'", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/object-inspect.git"}, "homepage": "https://github.com/inspect-js/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "browser": {"./util.inspect.js": false}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "support": true, "engines": {"node": ">= 0.4"}}