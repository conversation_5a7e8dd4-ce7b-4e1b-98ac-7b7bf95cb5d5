# Tunnel Configuration Example
# Copy this file to .env and update the values as needed

# Server Configuration
PORT=5000
NODE_ENV=development

# Tunnel Settings
TUNNEL_TYPE=ngrok
TUNNEL_PORT=5000
TUNNEL_SUBDOMAIN=eventapp-backend
TUNNEL_REGION=us

# Ngrok Configuration (optional)
# Get your auth token from https://dashboard.ngrok.com/get-started/your-authtoken
NGROK_AUTH_TOKEN=your_ngrok_auth_token_here

# CORS Configuration
# Add your tunnel URLs here when they're generated
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081,https://your-tunnel-url.ngrok.io

# Firebase Configuration (if using)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=your-client-email
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Security Settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
