# Deployment Guide

This guide covers deploying the Event App Backend to various platforms and environments.

## Table of Contents

1. [Pre-deployment Checklist](#pre-deployment-checklist)
2. [Environment Configuration](#environment-configuration)
3. [Platform-Specific Deployments](#platform-specific-deployments)
4. [Database Setup](#database-setup)
5. [Monitoring and Logging](#monitoring-and-logging)
6. [Security Considerations](#security-considerations)
7. [Performance Optimization](#performance-optimization)
8. [Troubleshooting](#troubleshooting)

## Pre-deployment Checklist

### Code Quality
- [ ] All tests passing (`npm test`)
- [ ] Code linting passed (`npm run lint`)
- [ ] Security audit passed (`npm audit`)
- [ ] Dependencies updated to latest stable versions
- [ ] No console.log statements in production code
- [ ] Error handling implemented for all endpoints

### Configuration
- [ ] Environment variables configured
- [ ] Firebase project set up for production
- [ ] Database indexes created
- [ ] Security rules deployed
- [ ] CORS origins configured for production domains
- [ ] Rate limiting configured appropriately

### Documentation
- [ ] API documentation updated
- [ ] Deployment documentation current
- [ ] Environment setup documented
- [ ] Monitoring setup documented

## Environment Configuration

### Environment Variables

Create production environment configuration:

```bash
# Production .env file
NODE_ENV=production
PORT=5000

# Firebase Configuration
FIREBASE_PROJECT_ID=your-production-project-id
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}

# Security Configuration
ALLOWED_ORIGINS=https://your-app.com,https://admin.your-app.com
JWT_SECRET=your-super-secret-jwt-key
API_KEY=your-api-key

# Performance Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
MAX_REQUEST_SIZE=10mb

# Monitoring Configuration
LOG_LEVEL=info
ENABLE_METRICS=true
SENTRY_DSN=your-sentry-dsn
```

### Environment-Specific Configurations

#### Development
```bash
NODE_ENV=development
PORT=5000
FIREBASE_PROJECT_ID=eventapp-dev
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081
LOG_LEVEL=debug
```

#### Staging
```bash
NODE_ENV=staging
PORT=5000
FIREBASE_PROJECT_ID=eventapp-staging
ALLOWED_ORIGINS=https://staging.your-app.com
LOG_LEVEL=info
```

#### Production
```bash
NODE_ENV=production
PORT=5000
FIREBASE_PROJECT_ID=eventapp-prod
ALLOWED_ORIGINS=https://your-app.com
LOG_LEVEL=warn
```

## Platform-Specific Deployments

### Heroku Deployment

#### 1. Prepare for Heroku
```bash
# Install Heroku CLI
npm install -g heroku

# Login to Heroku
heroku login

# Create Heroku app
heroku create your-app-name
```

#### 2. Configure Environment Variables
```bash
# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set FIREBASE_PROJECT_ID=your-project-id
heroku config:set FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account",...}'
heroku config:set ALLOWED_ORIGINS=https://your-app.com
```

#### 3. Deploy
```bash
# Deploy to Heroku
git push heroku main

# Check logs
heroku logs --tail
```

#### 4. Heroku-specific Configuration

Create `Procfile`:
```
web: npm start
```

Update `package.json`:
```json
{
  "engines": {
    "node": "18.x",
    "npm": "9.x"
  },
  "scripts": {
    "start": "node server.js",
    "heroku-postbuild": "npm run build"
  }
}
```

### Google Cloud Run Deployment

#### 1. Prepare Docker Configuration

Create `Dockerfile`:
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:5000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start application
CMD ["npm", "start"]
```

#### 2. Deploy to Cloud Run
```bash
# Build and deploy
gcloud run deploy eventapp-backend \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars NODE_ENV=production \
  --set-env-vars FIREBASE_PROJECT_ID=your-project-id
```

#### 3. Configure Environment Variables
```bash
# Set environment variables
gcloud run services update eventapp-backend \
  --set-env-vars FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account",...}' \
  --set-env-vars ALLOWED_ORIGINS=https://your-app.com
```

### AWS Elastic Beanstalk Deployment

#### 1. Prepare for AWS
```bash
# Install EB CLI
pip install awsebcli

# Initialize EB application
eb init

# Create environment
eb create production
```

#### 2. Configure Environment
Create `.ebextensions/environment.config`:
```yaml
option_settings:
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    FIREBASE_PROJECT_ID: your-project-id
    PORT: 5000
  aws:elasticbeanstalk:container:nodejs:
    NodeVersion: 18.17.0
    NodeCommand: "npm start"
```

#### 3. Deploy
```bash
# Deploy to AWS
eb deploy

# Check status
eb status
```

### DigitalOcean App Platform

#### 1. Create App Spec

Create `.do/app.yaml`:
```yaml
name: eventapp-backend
services:
- name: api
  source_dir: /
  github:
    repo: your-username/eventapp-backend
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
  - key: FIREBASE_PROJECT_ID
    value: your-project-id
  - key: FIREBASE_SERVICE_ACCOUNT_KEY
    value: your-service-account-key
    type: SECRET
  http_port: 5000
  health_check:
    http_path: /api/health
```

#### 2. Deploy
```bash
# Deploy using doctl
doctl apps create --spec .do/app.yaml
```

### Traditional VPS/Server Deployment

#### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx
```

#### 2. Application Setup
```bash
# Clone repository
git clone https://github.com/your-username/eventapp-backend.git
cd eventapp-backend

# Install dependencies
npm ci --only=production

# Create environment file
cp .env.example .env
# Edit .env with production values

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### 3. PM2 Configuration

Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'eventapp-backend',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

#### 4. Nginx Configuration

Create `/etc/nginx/sites-available/eventapp-backend`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable site:
```bash
sudo ln -s /etc/nginx/sites-available/eventapp-backend /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Database Setup

### Firebase Production Setup

#### 1. Create Production Project
```bash
# Create new Firebase project for production
firebase projects:create eventapp-prod

# Set up Firestore
firebase firestore:indexes:create --project eventapp-prod
```

#### 2. Deploy Security Rules
```bash
# Deploy Firestore rules
firebase deploy --only firestore:rules --project eventapp-prod

# Deploy Firestore indexes
firebase deploy --only firestore:indexes --project eventapp-prod
```

#### 3. Seed Production Data
```bash
# Set production environment
export FIREBASE_PROJECT_ID=eventapp-prod
export FIREBASE_SERVICE_ACCOUNT_KEY='...'

# Seed data
npm run seed-data
```

### Database Indexes

Create indexes for optimal performance:

```bash
# Bookings indexes
firebase firestore:indexes:create \
  --collection-group=bookings \
  --query-scope=COLLECTION \
  --fields="status:asc,createdAt:desc"

firebase firestore:indexes:create \
  --collection-group=bookings \
  --query-scope=COLLECTION \
  --fields="eventType:asc,eventDate:asc"

# Services indexes
firebase firestore:indexes:create \
  --collection-group=services \
  --query-scope=COLLECTION \
  --fields="category:asc,rating:desc"
```

## Monitoring and Logging

### Application Monitoring

#### 1. Health Checks
```javascript
// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version
  });
});
```

#### 2. Error Tracking with Sentry
```bash
# Install Sentry
npm install @sentry/node

# Configure Sentry
const Sentry = require('@sentry/node');

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV
});

app.use(Sentry.Handlers.requestHandler());
app.use(Sentry.Handlers.errorHandler());
```

#### 3. Performance Monitoring
```javascript
// Request duration tracking
const responseTime = require('response-time');

app.use(responseTime((req, res, time) => {
  console.log(`${req.method} ${req.url} - ${time}ms`);
}));
```

### Log Management

#### 1. Structured Logging
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

#### 2. Log Aggregation
- **ELK Stack**: Elasticsearch, Logstash, Kibana
- **Fluentd**: Log collection and forwarding
- **CloudWatch**: AWS log management
- **Google Cloud Logging**: GCP log management

## Security Considerations

### Production Security Checklist

- [ ] HTTPS enabled with valid SSL certificate
- [ ] Environment variables secured (no secrets in code)
- [ ] Firebase security rules properly configured
- [ ] Rate limiting enabled and configured
- [ ] CORS properly configured for production domains
- [ ] Security headers enabled (Helmet.js)
- [ ] Input validation on all endpoints
- [ ] Error messages don't expose sensitive information
- [ ] Dependencies regularly updated
- [ ] Security monitoring enabled

### SSL/TLS Configuration

#### Let's Encrypt with Certbot
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Security Headers

```javascript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

## Performance Optimization

### Production Optimizations

#### 1. Enable Compression
```javascript
const compression = require('compression');
app.use(compression());
```

#### 2. Caching Strategy
```javascript
// Cache static responses
app.use('/api/events', (req, res, next) => {
  res.set('Cache-Control', 'public, max-age=300'); // 5 minutes
  next();
});
```

#### 3. Connection Pooling
Firebase handles connection pooling automatically, but monitor connection usage.

#### 4. Memory Management
```javascript
// Monitor memory usage
setInterval(() => {
  const memUsage = process.memoryUsage();
  console.log('Memory usage:', {
    rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB',
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB'
  });
}, 60000); // Every minute
```

## Troubleshooting

### Common Issues

#### 1. Firebase Connection Issues
```bash
# Check Firebase credentials
node -e "console.log(JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY))"

# Test Firebase connection
npm run test-firebase
```

#### 2. Memory Issues
```bash
# Monitor memory usage
pm2 monit

# Restart if memory usage is high
pm2 restart eventapp-backend
```

#### 3. Performance Issues
```bash
# Check response times
curl -w "@curl-format.txt" -o /dev/null -s "http://your-domain.com/api/health"

# Monitor database performance
# Check Firebase console for slow queries
```

#### 4. SSL Certificate Issues
```bash
# Check certificate status
sudo certbot certificates

# Renew certificate
sudo certbot renew
```

### Debugging Production Issues

#### 1. Log Analysis
```bash
# Check application logs
pm2 logs eventapp-backend

# Check system logs
sudo journalctl -u nginx
```

#### 2. Health Monitoring
```bash
# Check health endpoint
curl https://your-domain.com/api/health

# Check system resources
htop
df -h
```

#### 3. Database Monitoring
- Monitor Firebase console for errors
- Check query performance
- Review security rule violations

### Rollback Strategy

#### 1. Application Rollback
```bash
# Rollback to previous version
git checkout previous-stable-tag
pm2 restart eventapp-backend
```

#### 2. Database Rollback
- Firebase doesn't support automatic rollbacks
- Implement backup and restore procedures
- Use Firebase export/import for data migration

#### 3. Configuration Rollback
```bash
# Revert environment variables
heroku config:set VARIABLE_NAME=previous-value

# Revert Nginx configuration
sudo cp /etc/nginx/sites-available/eventapp-backend.backup /etc/nginx/sites-available/eventapp-backend
sudo systemctl reload nginx
```

## Post-Deployment Verification

### Verification Checklist

- [ ] Health endpoint responding correctly
- [ ] All API endpoints functional
- [ ] Database connections working
- [ ] SSL certificate valid
- [ ] Monitoring systems active
- [ ] Error tracking configured
- [ ] Performance metrics baseline established
- [ ] Backup systems operational

### Load Testing

```bash
# Install artillery
npm install -g artillery

# Create load test configuration
# artillery.yml
config:
  target: 'https://your-domain.com'
  phases:
    - duration: 60
      arrivalRate: 10

scenarios:
  - name: "Health check"
    requests:
      - get:
          url: "/api/health"

# Run load test
artillery run artillery.yml
```

This deployment guide provides comprehensive instructions for deploying the Event App Backend to various platforms while ensuring security, performance, and reliability.
