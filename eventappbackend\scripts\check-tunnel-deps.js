#!/usr/bin/env node

const { spawn } = require('child_process');

function checkCommand(command, args = ['--version']) {
  return new Promise((resolve) => {
    const process = spawn(command, args, { stdio: 'pipe' });
    
    let output = '';
    process.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    process.stderr.on('data', (data) => {
      output += data.toString();
    });
    
    process.on('close', (code) => {
      resolve({
        available: code === 0,
        output: output.trim(),
        command
      });
    });
    
    process.on('error', () => {
      resolve({
        available: false,
        output: 'Command not found',
        command
      });
    });
    
    // Timeout after 5 seconds
    setTimeout(() => {
      process.kill();
      resolve({
        available: false,
        output: 'Timeout',
        command
      });
    }, 5000);
  });
}

async function checkDependencies() {
  console.log('🔍 Checking Tunnel Dependencies...\n');
  
  const checks = [
    { name: 'Node.js', command: 'node', args: ['--version'] },
    { name: 'NPM', command: 'npm', args: ['--version'] },
    { name: 'Cloudflared (Optional)', command: 'cloudflared', args: ['--version'] },
  ];
  
  const results = [];
  
  for (const check of checks) {
    console.log(`Checking ${check.name}...`);
    const result = await checkCommand(check.command, check.args);
    results.push({ ...check, ...result });
    
    if (result.available) {
      console.log(`✅ ${check.name}: ${result.output.split('\n')[0]}`);
    } else {
      console.log(`❌ ${check.name}: ${result.output}`);
    }
  }
  
  console.log('\n📦 NPM Package Dependencies:');
  
  try {
    const packageJson = require('../package.json');
    const tunnelDeps = ['ngrok', 'localtunnel', 'commander'];
    
    tunnelDeps.forEach(dep => {
      if (packageJson.devDependencies[dep]) {
        console.log(`✅ ${dep}: ${packageJson.devDependencies[dep]}`);
      } else {
        console.log(`❌ ${dep}: Not installed`);
      }
    });
  } catch (error) {
    console.log('❌ Could not read package.json');
  }
  
  console.log('\n🚇 Tunnel Service Availability:');
  console.log('✅ Ngrok: Available via npm package');
  console.log('✅ LocalTunnel: Available via npm package');
  
  const cloudflaredResult = results.find(r => r.command === 'cloudflared');
  if (cloudflaredResult?.available) {
    console.log('✅ Cloudflare Tunnel: Available via cloudflared CLI');
  } else {
    console.log('⚠️  Cloudflare Tunnel: cloudflared CLI not installed');
    console.log('   Install: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/');
  }
  
  console.log('\n📋 Quick Start:');
  console.log('1. npm run dev:tunnel     # Start with ngrok (recommended)');
  console.log('2. npm run dev:localtunnel # Start with LocalTunnel');
  if (cloudflaredResult?.available) {
    console.log('3. npm run dev:cloudflare  # Start with Cloudflare');
  }
  
  console.log('\n📚 For detailed setup instructions, see:');
  console.log('   docs/TUNNELING_GUIDE.md');
}

checkDependencies().catch(error => {
  console.error('❌ Dependency check failed:', error.message);
  process.exit(1);
});
