#!/usr/bin/env node

const TunnelManager = require('../tunnel-config');
const { program } = require('commander');

const tunnelManager = new TunnelManager();

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down tunnels...');
  await tunnelManager.stopAllTunnels();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Shutting down tunnels...');
  await tunnelManager.stopAllTunnels();
  process.exit(0);
});

program
  .name('tunnel')
  .description('Tunnel management for Event App Backend')
  .version('1.0.0');

program
  .command('start')
  .description('Start a tunnel')
  .option('-t, --type <type>', 'Tunnel type (ngrok, cloudflare, localtunnel)', 'ngrok')
  .option('-p, --port <port>', 'Port to tunnel', '5000')
  .option('-s, --subdomain <subdomain>', 'Subdomain for localtunnel', 'eventapp-backend')
  .option('-r, --region <region>', 'Region for ngrok', 'us')
  .action(async (options) => {
    const port = parseInt(options.port);
    
    try {
      switch (options.type.toLowerCase()) {
        case 'ngrok':
          await tunnelManager.startNgrok(port, { region: options.region });
          break;
        case 'cloudflare':
          await tunnelManager.startCloudflare(port);
          break;
        case 'localtunnel':
          await tunnelManager.startLocalTunnel(port, options.subdomain);
          break;
        default:
          console.error('❌ Invalid tunnel type. Use: ngrok, cloudflare, or localtunnel');
          process.exit(1);
      }
      
      tunnelManager.displayStatus();
      
      // Keep the process alive
      console.log('\n⏳ Tunnel is running. Press Ctrl+C to stop.');
      
    } catch (error) {
      console.error('❌ Failed to start tunnel:', error.message);
      process.exit(1);
    }
  });

program
  .command('stop')
  .description('Stop a tunnel')
  .option('-t, --type <type>', 'Tunnel type to stop (ngrok, cloudflare, localtunnel, all)', 'all')
  .action(async (options) => {
    try {
      if (options.type === 'all') {
        await tunnelManager.stopAllTunnels();
      } else {
        await tunnelManager.stopTunnel(options.type);
      }
    } catch (error) {
      console.error('❌ Failed to stop tunnel:', error.message);
      process.exit(1);
    }
  });

program
  .command('status')
  .description('Show tunnel status')
  .action(() => {
    tunnelManager.displayStatus();
  });

program
  .command('config')
  .description('Show tunnel configuration')
  .action(() => {
    const config = tunnelManager.loadConfig();
    console.log('\n📋 Tunnel Configuration:');
    console.log('========================');
    console.log(JSON.stringify(config, null, 2));
  });

program.parse();
