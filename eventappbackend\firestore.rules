rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isVendor() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor';
    }
    
    function isValidEmail(email) {
      return email.matches('.*@.*\\..*');
    }
    
    function isValidPhone(phone) {
      return phone.matches('[0-9]{10}');
    }
    
    // Events Collection - Read-only for all, write for admins
    match /events/{eventId} {
      allow read: if true; // Public read access
      allow write: if isAdmin();
    }
    
    // Services Collection - Read-only for all, write for admins and vendors
    match /services/{serviceId} {
      allow read: if true; // Public read access
      allow create, update: if isAdmin() || isVendor();
      allow delete: if isAdmin();
    }
    
    // Bookings Collection
    match /bookings/{bookingId} {
      // Read: Owner, assigned vendors, or admin
      allow read: if isAdmin() || 
                     (isAuthenticated() && resource.data.contactEmail == request.auth.token.email) ||
                     (isVendor() && request.auth.uid in resource.data.assignedVendors[].vendorId);
      
      // Create: Any authenticated user
      allow create: if isAuthenticated() && 
                       isValidEmail(request.resource.data.contactEmail) &&
                       isValidPhone(request.resource.data.contactPhone) &&
                       request.resource.data.status == 'pending' &&
                       request.resource.data.eventType in ['Birthday', 'Corporate', 'HouseWarming', 'Conference'];
      
      // Update: Owner (limited fields), assigned vendors (status updates), or admin
      allow update: if isAdmin() ||
                       (isAuthenticated() && 
                        resource.data.contactEmail == request.auth.token.email &&
                        // Only allow updating specific fields for customers
                        request.resource.data.diff(resource.data).affectedKeys()
                          .hasOnly(['eventDate', 'eventTime', 'location', 'guestCount', 'theme', 
                                   'services', 'serviceDetails', 'specialRequests', 'updatedAt'])) ||
                       (isVendor() && 
                        request.auth.uid in resource.data.assignedVendors[].vendorId &&
                        // Vendors can only update status and notes
                        request.resource.data.diff(resource.data).affectedKeys()
                          .hasOnly(['status', 'statusNotes', 'updatedAt']));
      
      // Delete: Only admin
      allow delete: if isAdmin();
    }
    
    // Users Collection
    match /users/{userId} {
      // Read: Own profile or admin
      allow read: if isOwner(userId) || isAdmin();
      
      // Create: Own profile only
      allow create: if isOwner(userId) && 
                       isValidEmail(request.resource.data.email) &&
                       request.resource.data.role in ['customer', 'vendor'];
      
      // Update: Own profile or admin
      allow update: if (isOwner(userId) && 
                        // Users cannot change their own role
                        request.resource.data.role == resource.data.role) ||
                       isAdmin();
      
      // Delete: Only admin
      allow delete: if isAdmin();
    }
    
    // Vendors Collection
    match /vendors/{vendorId} {
      // Read: Public read for active vendors, full access for owner/admin
      allow read: if resource.data.isActive == true || 
                     isOwner(resource.data.userId) || 
                     isAdmin();
      
      // Create: Authenticated users only
      allow create: if isAuthenticated() && 
                       isValidEmail(request.resource.data.email) &&
                       isValidPhone(request.resource.data.phone) &&
                       request.resource.data.isVerified == false;
      
      // Update: Owner or admin
      allow update: if isOwner(resource.data.userId) || isAdmin();
      
      // Delete: Only admin
      allow delete: if isAdmin();
    }
    
    // Reviews Collection
    match /reviews/{reviewId} {
      // Read: Public read for verified reviews
      allow read: if resource.data.isPublic == true && resource.data.isVerified == true;
      
      // Create: Authenticated users who have completed bookings
      allow create: if isAuthenticated() && 
                       exists(/databases/$(database)/documents/bookings/$(request.resource.data.bookingId)) &&
                       get(/databases/$(database)/documents/bookings/$(request.resource.data.bookingId)).data.status == 'completed' &&
                       get(/databases/$(database)/documents/bookings/$(request.resource.data.bookingId)).data.contactEmail == request.auth.token.email &&
                       request.resource.data.rating >= 1 && request.resource.data.rating <= 5;
      
      // Update: Owner or admin
      allow update: if isOwner(resource.data.userId) || isAdmin();
      
      // Delete: Owner or admin
      allow delete: if isOwner(resource.data.userId) || isAdmin();
    }
    
    // Health Collection - For health checks
    match /health/{document=**} {
      allow read, write: if true;
    }
  }
}
