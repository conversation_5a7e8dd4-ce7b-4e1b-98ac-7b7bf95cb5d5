# Testing Guide

Comprehensive guide for testing the Event App Backend, including unit tests, integration tests, and end-to-end testing strategies.

## Table of Contents

1. [Testing Philosophy](#testing-philosophy)
2. [Test Structure](#test-structure)
3. [Unit Testing](#unit-testing)
4. [Integration Testing](#integration-testing)
5. [API Testing](#api-testing)
6. [Database Testing](#database-testing)
7. [Performance Testing](#performance-testing)
8. [Security Testing](#security-testing)
9. [Test Automation](#test-automation)
10. [Best Practices](#best-practices)

## Testing Philosophy

### Testing Pyramid

```
    /\
   /  \     E2E Tests (Few)
  /____\    
 /      \   Integration Tests (Some)
/__________\ Unit Tests (Many)
```

- **Unit Tests (70%)**: Test individual functions and components
- **Integration Tests (20%)**: Test component interactions
- **E2E Tests (10%)**: Test complete user workflows

### Test-Driven Development (TDD)

1. **Red**: Write a failing test
2. **Green**: Write minimal code to pass
3. **Refactor**: Improve code while keeping tests green

## Test Structure

### Directory Structure

```
tests/
├── unit/                   # Unit tests
│   ├── utils/
│   │   └── schemaValidator.test.js
│   ├── middleware/
│   │   └── validation.test.js
│   └── config/
│       └── firebase.test.js
├── integration/            # Integration tests
│   ├── routes/
│   │   ├── events.test.js
│   │   ├── services.test.js
│   │   └── bookings.test.js
│   └── database/
│       └── firestore.test.js
├── e2e/                    # End-to-end tests
│   ├── booking-flow.test.js
│   └── admin-workflow.test.js
├── fixtures/               # Test data
│   ├── events.json
│   ├── services.json
│   └── bookings.json
├── helpers/                # Test utilities
│   ├── testSetup.js
│   ├── mockData.js
│   └── testHelpers.js
└── performance/            # Performance tests
    ├── load-test.js
    └── stress-test.js
```

### Test Configuration

#### Jest Configuration (`jest.config.js`)

```javascript
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/helpers/testSetup.js'],
  testMatch: [
    '<rootDir>/tests/**/*.test.js'
  ],
  collectCoverageFrom: [
    'routes/**/*.js',
    'middleware/**/*.js',
    'utils/**/*.js',
    'config/**/*.js',
    '!**/node_modules/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  testTimeout: 30000
};
```

#### Test Setup (`tests/helpers/testSetup.js`)

```javascript
const { MongoMemoryServer } = require('mongodb-memory-server');
const admin = require('firebase-admin');

// Global test setup
beforeAll(async () => {
  // Initialize test Firebase app
  if (!admin.apps.length) {
    admin.initializeApp({
      projectId: 'test-project',
      credential: admin.credential.applicationDefault()
    });
  }
});

afterAll(async () => {
  // Cleanup
  await admin.app().delete();
});

// Global test helpers
global.testHelpers = {
  generateTestId: () => `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  
  createTestUser: () => ({
    name: 'Test User',
    email: '<EMAIL>',
    phone: '**********'
  }),
  
  createTestBooking: () => ({
    eventType: 'Birthday',
    eventDate: '2024-12-25',
    eventTime: '18:00',
    location: 'Test Venue',
    guestCount: '21-50 guests',
    services: ['Decoration'],
    contactName: 'Test User',
    contactEmail: '<EMAIL>',
    contactPhone: '**********'
  })
};
```

## Unit Testing

### Testing Utilities

#### Schema Validator Tests (`tests/unit/utils/schemaValidator.test.js`)

```javascript
const SchemaValidator = require('../../../utils/schemaValidator');
const { bookingSchema } = require('../../../models/schemas');

describe('SchemaValidator', () => {
  describe('validateBooking', () => {
    it('should validate valid booking data', () => {
      const validBooking = {
        eventType: 'Birthday',
        eventDate: '2024-12-25',
        eventTime: '18:00',
        location: 'Test Venue',
        guestCount: '21-50 guests',
        services: ['Decoration'],
        contactName: 'John Doe',
        contactEmail: '<EMAIL>',
        contactPhone: '**********',
        status: 'pending',
        estimatedCost: 50000
      };

      const result = SchemaValidator.validateBooking(validBooking);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid event type', () => {
      const invalidBooking = {
        eventType: 'InvalidType',
        eventDate: '2024-12-25',
        // ... other required fields
      };

      const result = SchemaValidator.validateBooking(invalidBooking);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(
        expect.stringContaining('eventType')
      );
    });

    it('should reject missing required fields', () => {
      const incompleteBooking = {
        eventType: 'Birthday'
        // Missing required fields
      };

      const result = SchemaValidator.validateBooking(incompleteBooking);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('sanitize', () => {
    it('should remove undefined values', () => {
      const data = {
        name: 'John',
        age: undefined,
        email: '<EMAIL>'
      };

      const sanitized = SchemaValidator.sanitize(data, {
        name: { type: 'string' },
        age: { type: 'number' },
        email: { type: 'string' }
      });

      expect(sanitized).toEqual({
        name: 'John',
        email: '<EMAIL>'
      });
    });

    it('should apply default values', () => {
      const data = { name: 'John' };

      const sanitized = SchemaValidator.sanitize(data, {
        name: { type: 'string' },
        status: { type: 'string', default: 'active' }
      });

      expect(sanitized.status).toBe('active');
    });
  });
});
```

### Testing Middleware

#### Validation Middleware Tests (`tests/unit/middleware/validation.test.js`)

```javascript
const request = require('supertest');
const express = require('express');
const { validationSets } = require('../../../middleware/validation');

describe('Validation Middleware', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
  });

  describe('createBooking validation', () => {
    beforeEach(() => {
      app.post('/test', validationSets.createBooking, (req, res) => {
        res.json({ success: true });
      });
    });

    it('should pass with valid data', async () => {
      const validData = global.testHelpers.createTestBooking();

      const response = await request(app)
        .post('/test')
        .send(validData)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should fail with invalid email', async () => {
      const invalidData = {
        ...global.testHelpers.createTestBooking(),
        contactEmail: 'invalid-email'
      };

      const response = await request(app)
        .post('/test')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.details).toContain(
        expect.objectContaining({
          path: 'contactEmail'
        })
      );
    });
  });
});
```

## Integration Testing

### API Route Testing

#### Events API Tests (`tests/integration/routes/events.test.js`)

```javascript
const request = require('supertest');
const app = require('../../../server');

describe('Events API', () => {
  describe('GET /api/events', () => {
    it('should return all events', async () => {
      const response = await request(app)
        .get('/api/events')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.count).toBeGreaterThan(0);
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/events?limit=2&offset=1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
    });

    it('should support sorting', async () => {
      const response = await request(app)
        .get('/api/events?sort=createdAt&order=asc')
        .expect(200);

      expect(response.body.success).toBe(true);
      // Verify sorting order
      const dates = response.body.data.map(event => new Date(event.createdAt));
      for (let i = 1; i < dates.length; i++) {
        expect(dates[i]).toBeGreaterThanOrEqual(dates[i - 1]);
      }
    });
  });

  describe('GET /api/events/:type', () => {
    it('should return specific event type', async () => {
      const response = await request(app)
        .get('/api/events/Birthday')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.type).toBe('Birthday');
      expect(response.body.data.metadata).toBeDefined();
      expect(response.body.data.questions).toBeDefined();
    });

    it('should return 400 for invalid event type', async () => {
      const response = await request(app)
        .get('/api/events/InvalidType')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('Invalid event type');
    });
  });

  describe('GET /api/events/:type/questions', () => {
    it('should return event questions', async () => {
      const response = await request(app)
        .get('/api/events/Birthday/questions')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.questions).toBeInstanceOf(Array);
      expect(response.body.data.serviceQuestions).toBeDefined();
    });
  });
});
```

#### Bookings API Tests (`tests/integration/routes/bookings.test.js`)

```javascript
const request = require('supertest');
const app = require('../../../server');

describe('Bookings API', () => {
  let createdBookingId;

  describe('POST /api/bookings', () => {
    it('should create a new booking', async () => {
      const bookingData = global.testHelpers.createTestBooking();

      const response = await request(app)
        .post('/api/bookings')
        .send(bookingData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.bookingNumber).toBeDefined();
      expect(response.body.data.status).toBe('pending');

      createdBookingId = response.body.data.id;
    });

    it('should validate required fields', async () => {
      const incompleteData = {
        eventType: 'Birthday'
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/bookings')
        .send(incompleteData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.details).toBeDefined();
    });

    it('should validate email format', async () => {
      const invalidData = {
        ...global.testHelpers.createTestBooking(),
        contactEmail: 'invalid-email'
      };

      const response = await request(app)
        .post('/api/bookings')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/bookings/:id', () => {
    it('should return booking by ID', async () => {
      if (!createdBookingId) {
        // Create a booking first
        const bookingData = global.testHelpers.createTestBooking();
        const createResponse = await request(app)
          .post('/api/bookings')
          .send(bookingData);
        createdBookingId = createResponse.body.data.id;
      }

      const response = await request(app)
        .get(`/api/bookings/${createdBookingId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(createdBookingId);
    });

    it('should return 404 for non-existent booking', async () => {
      const response = await request(app)
        .get('/api/bookings/non-existent-id')
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/bookings/:id', () => {
    it('should update booking', async () => {
      if (!createdBookingId) {
        const bookingData = global.testHelpers.createTestBooking();
        const createResponse = await request(app)
          .post('/api/bookings')
          .send(bookingData);
        createdBookingId = createResponse.body.data.id;
      }

      const updateData = {
        location: 'Updated Venue',
        specialRequests: 'Updated requirements'
      };

      const response = await request(app)
        .put(`/api/bookings/${createdBookingId}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.location).toBe('Updated Venue');
    });
  });

  describe('PATCH /api/bookings/:id/status', () => {
    it('should update booking status', async () => {
      if (!createdBookingId) {
        const bookingData = global.testHelpers.createTestBooking();
        const createResponse = await request(app)
          .post('/api/bookings')
          .send(bookingData);
        createdBookingId = createResponse.body.data.id;
      }

      const response = await request(app)
        .patch(`/api/bookings/${createdBookingId}/status`)
        .send({
          status: 'confirmed',
          notes: 'Booking confirmed by admin'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('confirmed');
    });

    it('should validate status values', async () => {
      const response = await request(app)
        .patch(`/api/bookings/${createdBookingId}/status`)
        .send({
          status: 'invalid-status'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  // Cleanup
  afterAll(async () => {
    if (createdBookingId) {
      await request(app)
        .delete(`/api/bookings/${createdBookingId}`);
    }
  });
});
```

## Database Testing

### Firebase Testing

#### Firestore Rules Testing

```javascript
const firebase = require('@firebase/testing');
const fs = require('fs');

describe('Firestore Security Rules', () => {
  let testEnv;

  beforeAll(async () => {
    testEnv = await firebase.initializeTestEnvironment({
      projectId: 'test-project',
      firestore: {
        rules: fs.readFileSync('firestore.rules', 'utf8')
      }
    });
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  beforeEach(async () => {
    await testEnv.clearFirestore();
  });

  describe('Events Collection', () => {
    it('should allow read access to all users', async () => {
      const unauthedDb = testEnv.unauthenticatedContext().firestore();
      
      await firebase.assertSucceeds(
        unauthedDb.collection('events').get()
      );
    });

    it('should deny write access to unauthenticated users', async () => {
      const unauthedDb = testEnv.unauthenticatedContext().firestore();
      
      await firebase.assertFails(
        unauthedDb.collection('events').add({
          type: 'Birthday',
          title: 'Test Event'
        })
      );
    });
  });

  describe('Bookings Collection', () => {
    it('should allow users to read their own bookings', async () => {
      const userDb = testEnv.authenticatedContext('user123').firestore();
      
      // Create a booking
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await context.firestore().collection('bookings').doc('booking123').set({
          contactEmail: '<EMAIL>',
          eventType: 'Birthday'
        });
      });

      // User should be able to read their booking
      await firebase.assertSucceeds(
        userDb.collection('bookings').doc('booking123').get()
      );
    });

    it('should deny users from reading other users bookings', async () => {
      const userDb = testEnv.authenticatedContext('user123').firestore();
      
      // Create a booking for different user
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await context.firestore().collection('bookings').doc('booking456').set({
          contactEmail: '<EMAIL>',
          eventType: 'Birthday'
        });
      });

      // User should not be able to read other's booking
      await firebase.assertFails(
        userDb.collection('bookings').doc('booking456').get()
      );
    });
  });
});
```

## Performance Testing

### Load Testing with Artillery

#### Artillery Configuration (`artillery.yml`)

```yaml
config:
  target: 'http://localhost:5000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 120
      arrivalRate: 50
      name: "Load test"
    - duration: 60
      arrivalRate: 100
      name: "Stress test"
  payload:
    path: "test-data.csv"
    fields:
      - "eventType"
      - "contactEmail"

scenarios:
  - name: "Health check"
    weight: 20
    requests:
      - get:
          url: "/api/health"

  - name: "Get events"
    weight: 30
    requests:
      - get:
          url: "/api/events"

  - name: "Get services"
    weight: 30
    requests:
      - get:
          url: "/api/services"

  - name: "Create booking"
    weight: 20
    requests:
      - post:
          url: "/api/bookings"
          json:
            eventType: "{{ eventType }}"
            eventDate: "2024-12-25"
            eventTime: "18:00"
            location: "Test Venue"
            guestCount: "21-50 guests"
            services: ["Decoration"]
            contactName: "Test User"
            contactEmail: "{{ contactEmail }}"
            contactPhone: "**********"
```

#### Performance Test Script (`tests/performance/load-test.js`)

```javascript
const { execSync } = require('child_process');
const fs = require('fs');

describe('Performance Tests', () => {
  beforeAll(() => {
    // Generate test data
    const testData = [];
    for (let i = 0; i < 1000; i++) {
      testData.push([
        ['Birthday', 'Corporate', 'HouseWarming'][i % 3],
        `test${i}@example.com`
      ]);
    }
    
    const csvContent = 'eventType,contactEmail\n' + 
      testData.map(row => row.join(',')).join('\n');
    
    fs.writeFileSync('test-data.csv', csvContent);
  });

  it('should handle load test', (done) => {
    const command = 'npx artillery run artillery.yml --output report.json';
    
    try {
      execSync(command, { stdio: 'inherit' });
      
      // Generate HTML report
      execSync('npx artillery report report.json');
      
      // Parse results
      const report = JSON.parse(fs.readFileSync('report.json', 'utf8'));
      
      // Assert performance criteria
      expect(report.aggregate.latency.p95).toBeLessThan(1000); // 95th percentile < 1s
      expect(report.aggregate.errors).toBeLessThan(report.aggregate.requests * 0.01); // < 1% errors
      
      done();
    } catch (error) {
      done(error);
    }
  }, 300000); // 5 minute timeout

  afterAll(() => {
    // Cleanup
    if (fs.existsSync('test-data.csv')) {
      fs.unlinkSync('test-data.csv');
    }
    if (fs.existsSync('report.json')) {
      fs.unlinkSync('report.json');
    }
  });
});
```

## Security Testing

### Input Validation Testing

```javascript
describe('Security Tests', () => {
  describe('SQL Injection Protection', () => {
    it('should reject malicious input', async () => {
      const maliciousData = {
        ...global.testHelpers.createTestBooking(),
        contactName: "'; DROP TABLE bookings; --"
      };

      const response = await request(app)
        .post('/api/bookings')
        .send(maliciousData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('XSS Protection', () => {
    it('should sanitize script tags', async () => {
      const xssData = {
        ...global.testHelpers.createTestBooking(),
        specialRequests: '<script>alert("xss")</script>'
      };

      const response = await request(app)
        .post('/api/bookings')
        .send(xssData);

      // Should either reject or sanitize
      if (response.status === 201) {
        expect(response.body.data.specialRequests).not.toContain('<script>');
      } else {
        expect(response.status).toBe(400);
      }
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const requests = [];
      
      // Make multiple requests quickly
      for (let i = 0; i < 150; i++) {
        requests.push(
          request(app).get('/api/health')
        );
      }

      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    }, 30000);
  });
});
```

## Test Automation

### GitHub Actions Workflow (`.github/workflows/test.yml`)

```yaml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run unit tests
      run: npm run test:unit

    - name: Run integration tests
      run: npm run test:integration
      env:
        FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
        FIREBASE_SERVICE_ACCOUNT_KEY: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_KEY }}

    - name: Run security audit
      run: npm audit --audit-level moderate

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
```

### Package.json Test Scripts

```json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest tests/unit",
    "test:integration": "jest tests/integration",
    "test:e2e": "jest tests/e2e",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:performance": "jest tests/performance",
    "test:security": "npm audit && jest tests/security",
    "lint": "eslint . --ext .js",
    "lint:fix": "eslint . --ext .js --fix"
  }
}
```

## Best Practices

### Test Writing Guidelines

1. **Descriptive Test Names**: Use clear, descriptive test names
2. **Arrange-Act-Assert**: Structure tests clearly
3. **Independent Tests**: Each test should be independent
4. **Test Data**: Use factories or fixtures for test data
5. **Cleanup**: Always clean up after tests

### Mock Strategy

```javascript
// Mock external dependencies
jest.mock('../config/firebase', () => ({
  firestoreHelpers: {
    addDocument: jest.fn(),
    getDocument: jest.fn(),
    queryDocuments: jest.fn(),
    updateDocument: jest.fn(),
    deleteDocument: jest.fn()
  },
  collections: {
    EVENTS: 'events',
    SERVICES: 'services',
    BOOKINGS: 'bookings'
  }
}));
```

### Test Data Management

```javascript
// Test data factory
class TestDataFactory {
  static createEvent(overrides = {}) {
    return {
      type: 'Birthday',
      metadata: {
        title: 'Birthday Parties',
        description: 'Test description'
      },
      questions: [],
      serviceQuestions: {},
      ...overrides
    };
  }

  static createBooking(overrides = {}) {
    return {
      eventType: 'Birthday',
      eventDate: '2024-12-25',
      eventTime: '18:00',
      location: 'Test Venue',
      guestCount: '21-50 guests',
      services: ['Decoration'],
      contactName: 'Test User',
      contactEmail: '<EMAIL>',
      contactPhone: '**********',
      status: 'pending',
      estimatedCost: 50000,
      ...overrides
    };
  }
}
```

### Continuous Testing

1. **Pre-commit Hooks**: Run tests before commits
2. **CI/CD Integration**: Automated testing in pipelines
3. **Coverage Monitoring**: Track test coverage trends
4. **Performance Baselines**: Monitor performance regressions
5. **Security Scanning**: Regular security test execution

This comprehensive testing guide ensures robust, reliable, and secure code through systematic testing at all levels.
