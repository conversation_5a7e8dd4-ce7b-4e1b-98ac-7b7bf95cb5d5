# Tunneling Guide for Event App Backend

This guide explains how to expose your local Event App Backend to the internet for development purposes, allowing any frontend application to access your API from anywhere.

## 🚇 Available Tunneling Options

### 1. <PERSON>rok (Recommended)
- **Pros**: Reliable, fast, custom subdomains (paid), HTTPS by default
- **Cons**: Requires account for persistent URLs
- **Best for**: Professional development, team collaboration

### 2. Cloudflare Tunnel
- **Pros**: Free, fast, reliable, no account required
- **Cons**: Random URLs, requires cloudflared installation
- **Best for**: Quick testing, temporary access

### 3. LocalTunnel
- **Pros**: Simple, no account required, custom subdomains
- **Cons**: Can be unstable, slower than alternatives
- **Best for**: Quick prototyping, simple testing

## 📦 Installation

### Prerequisites
First, install the tunneling dependencies:

```bash
npm install
```

### Additional Tools (Optional)

#### For Cloudflare Tunnel:
```bash
# Windows
winget install --id Cloudflare.cloudflared

# macOS
brew install cloudflared

# Linux
wget -q https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
sudo dpkg -i cloudflared-linux-amd64.deb
```

## 🚀 Quick Start

### Method 1: Using NPM Scripts (Easiest)

```bash
# Start backend with ngrok tunnel
npm run dev:tunnel

# Start backend with Cloudflare tunnel
npm run dev:cloudflare

# Start backend with LocalTunnel
npm run dev:localtunnel
```

### Method 2: Using the Tunnel CLI

```bash
# Start a tunnel manually
node scripts/tunnel.js start --type ngrok --port 5000

# Check tunnel status
node scripts/tunnel.js status

# Stop all tunnels
node scripts/tunnel.js stop
```

### Method 3: Programmatic Usage

```javascript
const TunnelManager = require('./tunnel-config');

const tunnelManager = new TunnelManager();

// Start ngrok tunnel
const url = await tunnelManager.startNgrok(5000);
console.log(`Backend available at: ${url}`);
```

## 🔧 Configuration Options

### Ngrok Options
```bash
# Start with specific region
node scripts/tunnel.js start --type ngrok --region eu --port 5000

# Available regions: us, eu, ap, au, sa, jp, in
```

### LocalTunnel Options
```bash
# Start with custom subdomain
node scripts/tunnel.js start --type localtunnel --subdomain my-eventapp --port 5000
```

### Environment Variables
Create a `.env` file in your backend directory:

```env
# Tunnel Configuration
TUNNEL_TYPE=ngrok
TUNNEL_PORT=5000
TUNNEL_SUBDOMAIN=eventapp-backend
TUNNEL_REGION=us

# CORS Configuration for tunnels
ALLOWED_ORIGINS=https://your-tunnel-url.ngrok.io,http://localhost:3000
```

## 🌐 Frontend Integration

Once your tunnel is active, update your frontend configuration:

### React/Vue/Angular
```javascript
// config.js
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://your-production-api.com'
  : 'https://abc123.ngrok.io'; // Your tunnel URL

export { API_BASE_URL };
```

### Mobile Apps (React Native/Flutter)
```javascript
// For React Native
const API_BASE_URL = __DEV__ 
  ? 'https://abc123.ngrok.io'  // Your tunnel URL
  : 'https://your-production-api.com';
```

## 🔒 Security Considerations

### CORS Configuration
The backend automatically allows common development origins. For tunnels, update the CORS configuration:

```javascript
// In server.js, the CORS is already configured to read from environment
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? 
    process.env.ALLOWED_ORIGINS.split(',') : 
    ['http://localhost:3000', 'http://localhost:8081'],
  credentials: true
}));
```

### Rate Limiting
The backend includes rate limiting (100 requests per 15 minutes per IP). This helps protect against abuse when your API is publicly accessible.

### Environment-Specific Settings
```env
# Development tunnel settings
NODE_ENV=development
TUNNEL_AUTH_TOKEN=your_ngrok_auth_token  # For ngrok pro features
```

## 📱 Mobile Development

### Testing with Physical Devices
1. Start your tunnel: `npm run dev:tunnel`
2. Note the HTTPS URL (e.g., `https://abc123.ngrok.io`)
3. Update your mobile app's API configuration
4. Test on physical devices over cellular/WiFi

### QR Code Sharing
Many tunnel services provide QR codes for easy sharing:
- Ngrok: Visit the web interface at `http://localhost:4040`
- Share the tunnel URL via QR code with team members

## 🛠 Troubleshooting

### Common Issues

#### 1. Tunnel Not Starting
```bash
# Check if port is already in use
netstat -an | grep :5000

# Kill existing processes
npx kill-port 5000
```

#### 2. CORS Errors
Update your `.env` file:
```env
ALLOWED_ORIGINS=https://your-tunnel-url.ngrok.io,http://localhost:3000,http://localhost:8081
```

#### 3. Ngrok Authentication
```bash
# Set up ngrok auth token (for persistent URLs)
ngrok authtoken YOUR_AUTH_TOKEN
```

#### 4. Cloudflare Installation Issues
```bash
# Verify cloudflared installation
cloudflared --version

# Test tunnel manually
cloudflared tunnel --url http://localhost:5000
```

### Debug Mode
Enable debug logging:
```bash
DEBUG=tunnel* node scripts/tunnel.js start --type ngrok
```

## 📊 Monitoring and Logs

### Tunnel Status
```bash
# Check active tunnels
node scripts/tunnel.js status

# View tunnel configuration
node scripts/tunnel.js config
```

### Access Logs
- Ngrok: Web interface at `http://localhost:4040`
- Backend logs: Check console output for request logs
- Tunnel logs: Saved in `.tunnel-config.json`

## 🔄 Best Practices

1. **Use HTTPS**: All tunnel services provide HTTPS by default
2. **Environment Variables**: Store tunnel URLs in environment variables
3. **Team Sharing**: Share tunnel URLs securely with team members
4. **Temporary Use**: Remember tunnels are for development only
5. **Monitor Usage**: Keep an eye on request logs and rate limits
6. **Clean Shutdown**: Always stop tunnels when done (`Ctrl+C` or `npm run tunnel:stop`)

## 📚 Additional Resources

- [Ngrok Documentation](https://ngrok.com/docs)
- [Cloudflare Tunnel Documentation](https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/)
- [LocalTunnel Documentation](https://localtunnel.github.io/www/)

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review the console logs for error messages
3. Ensure your backend is running on the correct port
4. Verify your internet connection and firewall settings
