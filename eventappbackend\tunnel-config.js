const ngrok = require('ngrok');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class TunnelManager {
  constructor() {
    this.activeConnections = new Map();
    this.configFile = path.join(__dirname, '.tunnel-config.json');
  }

  // Load saved tunnel configuration
  loadConfig() {
    try {
      if (fs.existsSync(this.configFile)) {
        return JSON.parse(fs.readFileSync(this.configFile, 'utf8'));
      }
    } catch (error) {
      console.warn('Could not load tunnel config:', error.message);
    }
    return {};
  }

  // Save tunnel configuration
  saveConfig(config) {
    try {
      fs.writeFileSync(this.configFile, JSON.stringify(config, null, 2));
    } catch (error) {
      console.warn('Could not save tunnel config:', error.message);
    }
  }

  // Start ngrok tunnel
  async startNgrok(port = 5000, options = {}) {
    try {
      console.log('🚇 Starting ngrok tunnel...');

      // First, try to kill any existing ngrok processes
      await ngrok.kill().catch(() => {});

      const ngrokOptions = {
        addr: port,
        region: options.region || 'us',
        onStatusChange: status => {
          console.log(`Ngrok status: ${status}`);
        },
        onLogEvent: data => {
          console.log(`Ngrok log: ${data}`);
        },
        ...options
      };

      console.log('Connecting to ngrok...');
      const url = await ngrok.connect(ngrokOptions);
      this.activeConnections.set('ngrok', { url, port, type: 'ngrok' });

      console.log(`✅ Ngrok tunnel active: ${url}`);
      console.log(`📱 Your backend is now accessible from anywhere!`);

      // Save configuration
      const config = this.loadConfig();
      config.ngrok = { url, port, lastUsed: new Date().toISOString() };
      this.saveConfig(config);

      return url;
    } catch (error) {
      console.error('❌ Failed to start ngrok tunnel:', error.message);
      console.log('💡 Trying alternative approach...');

      // Try alternative approach using spawn
      return this.startNgrokAlternative(port, options);
    }
  }

  // Alternative ngrok startup method
  startNgrokAlternative(port = 5000, options = {}) {
    return new Promise((resolve, reject) => {
      console.log('🔄 Starting ngrok via command line...');

      const args = ['http', port.toString()];
      if (options.region) {
        args.push('--region', options.region);
      }

      const ngrokProcess = spawn('npx', ['ngrok', ...args], {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let url = null;
      let output = '';

      ngrokProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        console.log(text);

        // Look for tunnel URL in output
        const urlMatch = text.match(/https:\/\/[a-zA-Z0-9-]+\.ngrok\.io/);
        if (urlMatch && !url) {
          url = urlMatch[0];
          this.activeConnections.set('ngrok', { url, port, type: 'ngrok', process: ngrokProcess });

          console.log(`✅ Ngrok tunnel active: ${url}`);
          console.log(`📱 Your backend is now accessible from anywhere!`);

          // Save configuration
          const config = this.loadConfig();
          config.ngrok = { url, port, lastUsed: new Date().toISOString() };
          this.saveConfig(config);

          resolve(url);
        }
      });

      ngrokProcess.stderr.on('data', (data) => {
        const text = data.toString();
        console.error('Ngrok error:', text);

        // Also check stderr for URL (sometimes ngrok outputs there)
        const urlMatch = text.match(/https:\/\/[a-zA-Z0-9-]+\.ngrok\.io/);
        if (urlMatch && !url) {
          url = urlMatch[0];
          this.activeConnections.set('ngrok', { url, port, type: 'ngrok', process: ngrokProcess });

          console.log(`✅ Ngrok tunnel active: ${url}`);
          console.log(`📱 Your backend is now accessible from anywhere!`);

          resolve(url);
        }
      });

      ngrokProcess.on('close', (code) => {
        console.log(`Ngrok process exited with code ${code}`);
        this.activeConnections.delete('ngrok');
        if (!url) {
          reject(new Error(`Ngrok process exited with code ${code}`));
        }
      });

      ngrokProcess.on('error', (error) => {
        console.error('❌ Failed to start ngrok process:', error.message);
        reject(error);
      });

      // Timeout after 30 seconds if no URL is found
      setTimeout(() => {
        if (!url) {
          ngrokProcess.kill();
          reject(new Error('Ngrok tunnel startup timeout - no URL found'));
        }
      }, 30000);
    });
  }

  // Start Cloudflare tunnel
  startCloudflare(port = 5000) {
    return new Promise((resolve, reject) => {
      console.log('☁️ Starting Cloudflare tunnel...');
      
      const cloudflared = spawn('cloudflared', ['tunnel', '--url', `http://localhost:${port}`], {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let url = null;
      
      cloudflared.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(output);
        
        // Extract tunnel URL from cloudflared output
        const urlMatch = output.match(/https:\/\/[a-zA-Z0-9-]+\.trycloudflare\.com/);
        if (urlMatch && !url) {
          url = urlMatch[0];
          this.activeConnections.set('cloudflare', { url, port, type: 'cloudflare', process: cloudflared });
          
          console.log(`✅ Cloudflare tunnel active: ${url}`);
          console.log(`📱 Your backend is now accessible from anywhere!`);
          
          // Save configuration
          const config = this.loadConfig();
          config.cloudflare = { url, port, lastUsed: new Date().toISOString() };
          this.saveConfig(config);
          
          resolve(url);
        }
      });

      cloudflared.stderr.on('data', (data) => {
        console.error('Cloudflare tunnel error:', data.toString());
      });

      cloudflared.on('close', (code) => {
        console.log(`Cloudflare tunnel process exited with code ${code}`);
        this.activeConnections.delete('cloudflare');
      });

      cloudflared.on('error', (error) => {
        console.error('❌ Failed to start Cloudflare tunnel:', error.message);
        reject(error);
      });

      // Timeout after 30 seconds if no URL is found
      setTimeout(() => {
        if (!url) {
          cloudflared.kill();
          reject(new Error('Cloudflare tunnel startup timeout'));
        }
      }, 30000);
    });
  }

  // Start LocalTunnel
  async startLocalTunnel(port = 5000, subdomain = 'eventapp-backend') {
    try {
      console.log('🌐 Starting LocalTunnel...');

      // Try to use localtunnel module directly
      const localtunnel = require('localtunnel');

      const tunnel = await localtunnel({
        port: port,
        subdomain: subdomain
      });

      const url = tunnel.url;
      this.activeConnections.set('localtunnel', {
        url,
        port,
        type: 'localtunnel',
        tunnel: tunnel
      });

      console.log(`✅ LocalTunnel active: ${url}`);
      console.log(`📱 Your backend is now accessible from anywhere!`);

      // Save configuration
      const config = this.loadConfig();
      config.localtunnel = { url, port, lastUsed: new Date().toISOString() };
      this.saveConfig(config);

      // Handle tunnel close
      tunnel.on('close', () => {
        console.log('LocalTunnel closed');
        this.activeConnections.delete('localtunnel');
      });

      tunnel.on('error', (err) => {
        console.error('LocalTunnel error:', err.message);
      });

      return url;

    } catch (error) {
      console.error('❌ Failed to start LocalTunnel:', error.message);
      throw error;
    }
  }

  // Get all active tunnels
  getActiveTunnels() {
    return Array.from(this.activeConnections.values());
  }

  // Stop a specific tunnel
  async stopTunnel(type) {
    const connection = this.activeConnections.get(type);
    if (!connection) {
      console.log(`No active ${type} tunnel found`);
      return;
    }

    try {
      if (type === 'ngrok') {
        // Try to disconnect using ngrok API first
        try {
          await ngrok.disconnect();
          console.log('✅ Ngrok tunnel stopped via API');
        } catch (apiError) {
          // If API fails, try to kill the process
          if (connection.process) {
            connection.process.kill();
            console.log('✅ Ngrok tunnel stopped via process kill');
          } else {
            // Last resort: kill all ngrok processes
            await ngrok.kill();
            console.log('✅ Ngrok tunnel stopped via kill all');
          }
        }
      } else if (connection.process) {
        connection.process.kill();
        console.log(`✅ ${type} tunnel stopped`);
      } else if (connection.tunnel && typeof connection.tunnel.close === 'function') {
        connection.tunnel.close();
        console.log(`✅ ${type} tunnel stopped`);
      }

      this.activeConnections.delete(type);
    } catch (error) {
      console.error(`❌ Failed to stop ${type} tunnel:`, error.message);
      // Force remove from active connections even if stop failed
      this.activeConnections.delete(type);
    }
  }

  // Stop all tunnels
  async stopAllTunnels() {
    const types = Array.from(this.activeConnections.keys());
    for (const type of types) {
      await this.stopTunnel(type);
    }
  }

  // Display tunnel status
  displayStatus() {
    const tunnels = this.getActiveTunnels();
    
    if (tunnels.length === 0) {
      console.log('📡 No active tunnels');
      return;
    }

    console.log('\n📡 Active Tunnels:');
    console.log('==================');
    tunnels.forEach(tunnel => {
      console.log(`${tunnel.type.toUpperCase()}: ${tunnel.url}`);
      console.log(`Port: ${tunnel.port}`);
      console.log('---');
    });
    
    console.log('\n🔗 Use these URLs in your frontend applications');
    console.log('💡 Remember to update CORS settings if needed');
  }
}

module.exports = TunnelManager;
