const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let db;

const initializeFirebase = () => {
  try {
    // Check if Firebase is already initialized
    if (admin.apps.length === 0) {
      // Initialize with service account key (for production)
      if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          projectId: serviceAccount.project_id,
          databaseURL: process.env.FIREBASE_DATABASE_URL
        });
        console.log(`🔥 Firebase initialized with service account for project: ${serviceAccount.project_id}`);
      }
      // Initialize with default credentials (for development/local testing)
      else if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        admin.initializeApp({
          credential: admin.credential.applicationDefault(),
          projectId: process.env.FIREBASE_PROJECT_ID,
          databaseURL: process.env.FIREBASE_DATABASE_URL
        });
        console.log(`🔥 Firebase initialized with application default credentials for project: ${process.env.FIREBASE_PROJECT_ID}`);
      }
      // Initialize with project ID only (for emulator or testing)
      else {
        admin.initializeApp({
          projectId: process.env.FIREBASE_PROJECT_ID || 'eventapp-demo'
        });
        console.log(`🔥 Firebase initialized with project ID: ${process.env.FIREBASE_PROJECT_ID || 'eventapp-demo'}`);
      }
    }

    // Get Firestore instance
    db = admin.firestore();

    // Configure Firestore settings
    db.settings({
      ignoreUndefinedProperties: true
    });

    console.log('✅ Firebase initialized successfully');
    return db;
  } catch (error) {
    console.error('❌ Firebase initialization error:', error.message);
    console.error('🔧 Please check:');
    console.error('1. Firebase project exists and Firestore is enabled');
    console.error('2. Service account credentials are correct');
    console.error('3. Project ID matches your Firebase project');
    throw error;
  }
};

// Get Firestore database instance
const getDb = () => {
  if (!db) {
    return initializeFirebase();
  }
  return db;
};

// Collection references
const collections = {
  EVENTS: 'events',
  SERVICES: 'services',
  BOOKINGS: 'bookings',
  USERS: 'users',
  VENDORS: 'vendors'
};

// Helper functions for common operations
const firestoreHelpers = {
  // Add document with auto-generated ID
  async addDocument(collectionName, data) {
    const docRef = await getDb().collection(collectionName).add({
      ...data,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    return docRef.id;
  },

  // Get document by ID
  async getDocument(collectionName, docId) {
    const doc = await getDb().collection(collectionName).doc(docId).get();
    if (!doc.exists) {
      return null;
    }
    return { id: doc.id, ...doc.data() };
  },

  // Get all documents from collection
  async getCollection(collectionName, limit = null, orderBy = null) {
    let query = getDb().collection(collectionName);
    
    if (orderBy) {
      query = query.orderBy(orderBy.field, orderBy.direction || 'asc');
    }
    
    if (limit) {
      query = query.limit(limit);
    }
    
    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  },

  // Update document
  async updateDocument(collectionName, docId, data) {
    await getDb().collection(collectionName).doc(docId).update({
      ...data,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
  },

  // Delete document
  async deleteDocument(collectionName, docId) {
    await getDb().collection(collectionName).doc(docId).delete();
  },

  // Query documents with conditions
  async queryDocuments(collectionName, conditions = [], limit = null, orderBy = null) {
    let query = getDb().collection(collectionName);
    
    // Apply where conditions
    conditions.forEach(condition => {
      query = query.where(condition.field, condition.operator, condition.value);
    });
    
    if (orderBy) {
      query = query.orderBy(orderBy.field, orderBy.direction || 'asc');
    }
    
    if (limit) {
      query = query.limit(limit);
    }
    
    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }
};

module.exports = {
  admin,
  getDb,
  collections,
  firestoreHelpers,
  initializeFirebase
};
