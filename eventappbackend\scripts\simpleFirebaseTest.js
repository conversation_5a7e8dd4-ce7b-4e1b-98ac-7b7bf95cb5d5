/**
 * Simple Firebase Test
 * Tests basic Firebase connection without complex initialization
 */

require('dotenv').config();
const admin = require('firebase-admin');

async function simpleFirebaseTest() {
  console.log('🔥 Simple Firebase Connection Test\n');
  
  try {
    // Parse service account from environment
    const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
    
    console.log('📋 Service Account Info:');
    console.log(`- Project ID: ${serviceAccount.project_id}`);
    console.log(`- Client Email: ${serviceAccount.client_email}`);
    console.log('');
    
    // Initialize Firebase Admin
    if (admin.apps.length === 0) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.project_id
      });
    }
    
    console.log('✅ Firebase Admin SDK initialized');
    
    // Get Firestore instance
    const db = admin.firestore();
    console.log('✅ Firestore instance created');
    
    // Test 1: Try to create a simple document
    console.log('🧪 Test 1: Creating a test document...');
    const testDoc = {
      message: 'Hello from Firebase!',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      test: true
    };
    
    const docRef = await db.collection('test').add(testDoc);
    console.log(`✅ Test document created with ID: ${docRef.id}`);
    
    // Test 2: Try to read the document
    console.log('🧪 Test 2: Reading the test document...');
    const doc = await docRef.get();
    if (doc.exists) {
      console.log('✅ Test document read successfully');
      console.log('📄 Document data:', doc.data());
    } else {
      console.log('❌ Test document not found');
    }
    
    // Test 3: Try to list collections
    console.log('🧪 Test 3: Listing collections...');
    const collections = await db.listCollections();
    console.log(`✅ Found ${collections.length} collections:`);
    collections.forEach(collection => {
      console.log(`  - ${collection.id}`);
    });
    
    // Clean up: Delete test document
    console.log('🧹 Cleaning up test document...');
    await docRef.delete();
    console.log('✅ Test document deleted');
    
    console.log('\n🎉 All Firebase tests passed! Your Firebase setup is working correctly.');
    console.log('\n📝 Next steps:');
    console.log('1. Your Firebase connection is working');
    console.log('2. You can now run: npm run init-db');
    console.log('3. Then start your server: npm run dev');
    
  } catch (error) {
    console.error('\n❌ Firebase test failed:', error.message);
    
    if (error.code === 5) {
      console.log('\n🔧 This is a NOT_FOUND error. Possible causes:');
      console.log('1. Firestore database has not been created in your Firebase project');
      console.log('2. The project ID might be incorrect');
      console.log('3. Firestore API might not be enabled');
      
      console.log('\n📋 To fix this:');
      console.log('1. Go to: https://console.firebase.google.com/project/easemyevent-27729');
      console.log('2. Click on "Firestore Database" in the sidebar');
      console.log('3. If you see "Get started", click it and create a database');
      console.log('4. Choose "Start in test mode"');
      console.log('5. Select a location (e.g., asia-south1 for India)');
      console.log('6. Wait a few minutes for the database to be created');
    }
    
    if (error.code === 7) {
      console.log('\n🔧 This is a PERMISSION_DENIED error. Possible causes:');
      console.log('1. Firestore API is not enabled');
      console.log('2. Service account doesn\'t have proper permissions');
      
      console.log('\n📋 To fix this:');
      console.log('1. Enable Firestore API: https://console.developers.google.com/apis/api/firestore.googleapis.com/overview?project=easemyevent-27729');
      console.log('2. Ensure your service account has Firestore permissions');
    }
    
    console.log('\n📚 Resources:');
    console.log('- Firebase Console: https://console.firebase.google.com/');
    console.log('- Enable APIs: https://console.developers.google.com/');
  }
}

// Run the test
if (require.main === module) {
  simpleFirebaseTest()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test script failed:', error);
      process.exit(1);
    });
}

module.exports = simpleFirebaseTest;
