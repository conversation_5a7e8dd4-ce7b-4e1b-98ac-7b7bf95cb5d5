import { API_CONFIG, apiRequest, ApiResponse } from '../../constants/Api';
import { Service } from '../../types/service';

// Services API functions
export const servicesApi = {
  // Get all services
  getAll: async (params?: {
    limit?: number;
    offset?: number;
    sort?: string;
    order?: 'asc' | 'desc';
  }): Promise<ApiResponse<Service[]>> => {
    return apiRequest<Service[]>(API_CONFIG.ENDPOINTS.SERVICES, {}, params);
  },

  // Get service by ID
  getById: async (id: string): Promise<ApiResponse<Service>> => {
    return apiRequest<Service>(`${API_CONFIG.ENDPOINTS.SERVICES}/${id}`);
  },

  // Get services by category
  getByCategory: async (category: string): Promise<ApiResponse<Service[]>> => {
    return apiRequest<Service[]>(`${API_CONFIG.ENDPOINTS.SERVICES}/category/${category}`);
  },

  // Search services
  search: async (searchTerm: string, limit?: number): Promise<ApiResponse<Service[]>> => {
    const params: Record<string, string | number> = { q: searchTerm };
    if (limit) params.limit = limit;
    
    return apiRequest<Service[]>(`${API_CONFIG.ENDPOINTS.SERVICES}/search`, {}, params);
  },

  // Seed services (for development/testing)
  seed: async (): Promise<ApiResponse<{ message: string }>> => {
    return apiRequest<{ message: string }>(`${API_CONFIG.ENDPOINTS.SERVICES}/seed`, {
      method: 'POST',
    });
  },
};
