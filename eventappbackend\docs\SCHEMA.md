# Firebase Firestore Schema Documentation

This document outlines the database schema for the Event Management App using Firebase Firestore.

## Overview

The database uses the following collections:
- `events` - Event type definitions and questions
- `services` - Service catalog with packages and pricing
- `bookings` - Customer bookings and reservations
- `users` - User profiles and authentication data
- `vendors` - Vendor/service provider information
- `reviews` - Customer reviews and ratings

## Collection Schemas

### Events Collection

Stores event type definitions with associated questions and metadata.

```javascript
{
  "type": "Birthday",                    // Event type identifier
  "metadata": {
    "title": "Birthday Parties",
    "description": "Memorable birthday celebrations",
    "images": ["url1", "url2"],         // Array of image URLs
    "icon": "cake",
    "path": "/event/booking?type=birthday"
  },
  "questions": [                        // Event-specific questions
    {
      "id": "guestCount",
      "text": "How many guests are you expecting?",
      "type": "select",                 // text, select, multiselect, date, time, number
      "options": ["1-20 guests", "21-50 guests"],
      "required": true
    }
  ],
  "serviceQuestions": {                 // Service-specific questions
    "Decoration": [
      {
        "id": "theme",
        "text": "What is the decoration theme?",
        "type": "text",
        "required": false
      }
    ]
  },
  "isStatic": true,                     // Indicates seeded data
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### Services Collection

Contains service catalog with packages, pricing, and features.

```javascript
{
  "id": "decoration",                   // Service identifier
  "title": "Decoration",
  "description": "Transform your venue with stunning decoration",
  "longDescription": "Detailed service description...",
  "icon": "🎨",
  "category": "decoration",             // decoration, catering, photography, etc.
  "features": [                         // Array of service features
    "Custom theme-based decoration",
    "Premium quality materials"
  ],
  "packages": [                         // Service packages
    {
      "name": "Basic",
      "price": "₹25,000 - ₹50,000",
      "includes": [
        "Basic venue decoration",
        "Standard floral arrangements"
      ]
    }
  ],
  "images": ["url1", "url2"],          // Service gallery
  "rating": 4.5,                       // Average rating (0-5)
  "reviewCount": 150,                  // Number of reviews
  "isActive": true,                    // Service availability
  "isStatic": true,                    // Indicates seeded data
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### Bookings Collection

Stores customer bookings with event details and status tracking.

```javascript
{
  "bookingNumber": "EVT-123456-ABC123", // Unique booking reference
  
  // Event Details
  "eventType": "Birthday",
  "eventDate": "2024-12-25",           // ISO date string
  "eventTime": "18:00",                // HH:MM format
  "location": "Sample Venue, Mumbai",
  "guestCount": "21-50 guests",
  "theme": "Superhero Theme",
  
  // Services
  "services": ["Decoration", "Catering"], // Selected services
  "serviceDetails": {                   // Service-specific answers
    "Decoration": {
      "theme": "Superhero",
      "balloons": "Yes"
    }
  },
  
  // Contact Information
  "contactName": "John Doe",
  "contactEmail": "<EMAIL>",
  "contactPhone": "**********",
  "alternatePhone": "**********",
  
  // Booking Status
  "status": "pending",                 // pending, confirmed, in-progress, completed, cancelled
  "statusNotes": "Waiting for venue confirmation",
  
  // Pricing
  "estimatedCost": 75000,              // Estimated cost in INR
  "finalCost": 80000,                  // Final agreed cost
  "advanceAmount": 20000,              // Advance paid
  "balanceAmount": 60000,              // Remaining balance
  
  // Additional Information
  "specialRequests": "Vegan food options required",
  "internalNotes": "Customer prefers morning setup",
  
  // Vendor Assignment
  "assignedVendors": [
    {
      "serviceType": "Decoration",
      "vendorId": "vendor123",
      "vendorName": "ABC Decorators",
      "contactInfo": "**********",
      "assignedAt": "2024-01-01T00:00:00Z"
    }
  ],
  
  // Timestamps
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z",
  "confirmedAt": "2024-01-02T00:00:00Z",
  "completedAt": "2024-01-25T00:00:00Z"
}
```

### Users Collection

User profiles and authentication data.

```javascript
{
  // Document ID: Firebase Auth UID
  "email": "<EMAIL>",
  "displayName": "John Doe",
  "phoneNumber": "**********",
  "photoURL": "https://example.com/photo.jpg",
  "role": "customer",                  // customer, vendor, admin
  
  // Profile Information
  "profile": {
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "address": {
      "street": "123 Main St",
      "city": "Mumbai",
      "state": "Maharashtra",
      "zipCode": "400001",
      "country": "India"
    }
  },
  
  // Preferences
  "preferences": {
    "favoriteEventTypes": ["Birthday", "Corporate"],
    "preferredServices": ["Decoration", "Photography"],
    "budgetRange": "************",
    "notifications": {
      "email": true,
      "sms": true,
      "push": true
    }
  },
  
  // Account Status
  "isActive": true,
  "isVerified": false,
  "lastLoginAt": "2024-01-01T00:00:00Z",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### Vendors Collection

Service provider information and business details.

```javascript
{
  "businessName": "ABC Event Decorators",
  "ownerName": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "**********",
  "alternatePhone": "**********",
  
  // Business Information
  "services": ["Decoration", "Lighting"],
  "serviceAreas": ["Mumbai", "Pune", "Nashik"],
  "businessAddress": {
    "street": "456 Business St",
    "city": "Mumbai",
    "state": "Maharashtra",
    "zipCode": "400002",
    "country": "India"
  },
  
  // Business Details
  "description": "Professional event decoration services",
  "experience": 5,                     // Years of experience
  "teamSize": 10,
  "portfolio": ["url1", "url2"],       // Portfolio images
  
  // Ratings and Reviews
  "rating": 4.5,
  "reviewCount": 100,
  "completedBookings": 250,
  
  // Verification and Status
  "isVerified": true,
  "isActive": true,
  "verificationDocuments": [
    {
      "type": "license",
      "url": "document-url",
      "expiryDate": "2025-12-31",
      "verified": true
    }
  ],
  
  // Timestamps
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z",
  "verifiedAt": "2024-01-15T00:00:00Z"
}
```

### Reviews Collection

Customer reviews and ratings for vendors.

```javascript
{
  "bookingId": "booking123",
  "userId": "user123",
  "vendorId": "vendor123",
  "serviceType": "Decoration",
  
  // Review Content
  "rating": 5,                         // 1-5 stars
  "title": "Excellent service!",
  "comment": "Amazing decoration work, highly recommended",
  "images": ["review-photo1.jpg"],     // Review photos
  
  // Review Status
  "isVerified": true,
  "isPublic": true,
  
  // Timestamps
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

## Indexes

The following indexes should be created for optimal query performance:

### Bookings
- `status` (asc) + `createdAt` (desc)
- `eventType` (asc) + `eventDate` (asc)
- `contactEmail` (asc) + `createdAt` (desc)

### Services
- `category` (asc) + `rating` (desc)
- `isActive` (asc) + `createdAt` (desc)

### Vendors
- `isActive` (asc) + `isVerified` (asc) + `rating` (desc)

### Reviews
- `vendorId` (asc) + `isVerified` (asc) + `createdAt` (desc)

## Security Rules

Security rules are defined in `firestore.rules` and include:

- **Public read access** for events and services
- **Authenticated access** for creating bookings
- **Owner/admin access** for modifying user data
- **Vendor access** for updating assigned bookings
- **Admin access** for all administrative operations

## Data Validation

All data is validated using the schema definitions in `models/schemas.js` before being saved to Firestore. The validation includes:

- **Type checking** (string, number, boolean, array, object)
- **Required field validation**
- **Enum value validation**
- **Format validation** (email, phone, date, time)
- **Range validation** (min/max values)
- **Custom business logic validation**

## Migration and Seeding

Initial data is seeded from JSON files:
- `EventData.json` → `events` collection
- `ServiceData.json` → `services` collection

Use the initialization script:
```bash
node scripts/initDatabase.js
```

This script will:
1. Create necessary indexes
2. Seed initial data
3. Validate existing data
4. Set up database structure
