const express = require('express');
const { firestoreHelpers, collections } = require('../config/firebase');
const { validationSets } = require('../middleware/validation');
const router = express.Router();

// Import service data (for initial seeding)
const serviceData = require('../../eventapp/data/ServiceData.json');

// @desc    Get all services
// @route   GET /api/services
// @access  Public
router.get('/', validationSets.getWithQuery, async (req, res) => {
  try {
    const { limit = 50, offset = 0, sort = 'createdAt', order = 'desc' } = req.query;

    // Try to get from Firebase first
    let services = await firestoreHelpers.getCollection(
      collections.SERVICES,
      parseInt(limit),
      { field: sort, direction: order }
    );

    // If no services in Firebase, seed with static data
    if (services.length === 0) {
      console.log('No services found in Firebase, seeding with static data...');
      await seedServiceData();
      services = await firestoreHelpers.getCollection(
        collections.SERVICES,
        parseInt(limit),
        { field: sort, direction: order }
      );
    }

    // Apply offset manually
    const offsetServices = services.slice(parseInt(offset));

    res.json({
      success: true,
      count: offsetServices.length,
      total: services.length,
      data: offsetServices
    });
  } catch (error) {
    console.error('Error fetching services:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch services',
        details: error.message
      }
    });
  }
});

// @desc    Get service by ID
// @route   GET /api/services/:id
// @access  Public
router.get('/:id', validationSets.getById, async (req, res) => {
  try {
    const { id } = req.params;

    // Try to get from Firebase
    let service = await firestoreHelpers.getDocument(collections.SERVICES, id);

    // If not found, try to find in static data by service ID
    if (!service && serviceData.services) {
      const staticService = serviceData.services.find(s => s.id === id);
      if (staticService) {
        // Save to Firebase for future requests
        try {
          const docId = await firestoreHelpers.addDocument(collections.SERVICES, {
            ...staticService,
            isStatic: true
          });
          service = { id: docId, ...staticService, isStatic: true };
        } catch (error) {
          console.warn('Could not save to Firebase, returning static data:', error.message);
          service = { id, ...staticService, isStatic: true };
        }
      }
    }

    if (!service) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Service not found'
        }
      });
    }

    res.json({
      success: true,
      data: service
    });
  } catch (error) {
    console.error('Error fetching service:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch service',
        details: error.message
      }
    });
  }
});

// @desc    Get services by category
// @route   GET /api/services/category/:category
// @access  Public
router.get('/category/:category', async (req, res) => {
  try {
    const { category } = req.params;

    // Get services from Firebase
    let services = await firestoreHelpers.queryDocuments(
      collections.SERVICES,
      [{ field: 'category', operator: '==', value: category }]
    );

    // If no services found, check static data
    if (services.length === 0 && serviceData.services) {
      const staticServices = serviceData.services.filter(s => 
        s.category && s.category.toLowerCase() === category.toLowerCase()
      );
      
      if (staticServices.length > 0) {
        // Optionally save to Firebase
        try {
          const promises = staticServices.map(service => 
            firestoreHelpers.addDocument(collections.SERVICES, {
              ...service,
              isStatic: true
            })
          );
          await Promise.all(promises);
          
          // Fetch the newly saved services
          services = await firestoreHelpers.queryDocuments(
            collections.SERVICES,
            [{ field: 'category', operator: '==', value: category }]
          );
        } catch (error) {
          console.warn('Could not save to Firebase, returning static data:', error.message);
          services = staticServices.map((service, index) => ({
            id: `${category}-${index}`,
            ...service,
            isStatic: true
          }));
        }
      }
    }

    res.json({
      success: true,
      count: services.length,
      data: services
    });
  } catch (error) {
    console.error('Error fetching services by category:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch services by category',
        details: error.message
      }
    });
  }
});

// @desc    Search services
// @route   GET /api/services/search?q=searchterm
// @access  Public
router.get('/search', async (req, res) => {
  try {
    const { q: searchTerm, limit = 20 } = req.query;

    if (!searchTerm) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Search term is required'
        }
      });
    }

    // Get all services (Firebase doesn't support full-text search natively)
    let allServices = await firestoreHelpers.getCollection(collections.SERVICES);

    // If no services, seed first
    if (allServices.length === 0) {
      await seedServiceData();
      allServices = await firestoreHelpers.getCollection(collections.SERVICES);
    }

    // Perform client-side search
    const searchResults = allServices.filter(service => {
      const searchFields = [
        service.title,
        service.description,
        service.longDescription,
        ...(service.features || []),
        service.category
      ].join(' ').toLowerCase();

      return searchFields.includes(searchTerm.toLowerCase());
    }).slice(0, parseInt(limit));

    res.json({
      success: true,
      count: searchResults.length,
      searchTerm,
      data: searchResults
    });
  } catch (error) {
    console.error('Error searching services:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to search services',
        details: error.message
      }
    });
  }
});

// Helper function to seed service data
async function seedServiceData() {
  try {
    if (!serviceData.services || !Array.isArray(serviceData.services)) {
      throw new Error('Invalid service data format');
    }

    const promises = serviceData.services.map(async (service) => {
      return firestoreHelpers.addDocument(collections.SERVICES, {
        ...service,
        isStatic: true
      });
    });

    await Promise.all(promises);
    console.log('✅ Service data seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding service data:', error);
    throw error;
  }
}

// @desc    Seed service data (admin endpoint)
// @route   POST /api/services/seed
// @access  Public (should be protected in production)
router.post('/seed', async (req, res) => {
  try {
    await seedServiceData();
    res.json({
      success: true,
      message: 'Service data seeded successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to seed service data',
        details: error.message
      }
    });
  }
});

module.exports = router;
