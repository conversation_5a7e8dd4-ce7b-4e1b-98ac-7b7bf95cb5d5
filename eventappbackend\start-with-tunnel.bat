@echo off
echo ========================================
echo   Event App Backend with Stable Tunnel
echo ========================================
echo.
echo Starting backend server and tunnel...
echo.
echo Backend will be available at:
echo   Local: http://localhost:5000
echo   Tunnel: https://eventapp-api-stable.loca.lt
echo.
echo Press Ctrl+C to stop both server and tunnel
echo.

REM Start the backend server in background
start "Backend Server" cmd /c "npm run dev"

REM Wait a moment for server to start
timeout /t 3 /nobreak >nul

REM Start the tunnel
npx localtunnel --port 5000 --subdomain eventapp-api-stable
