/**
 * Simple Data Seeding Script
 * Seeds event and service data to Firestore
 */

require('dotenv').config();
const admin = require('firebase-admin');

// Import data
const eventData = require('../../eventapp/data/EventData.json');
const serviceData = require('../../eventapp/data/ServiceData.json');

async function seedData() {
  console.log('🌱 Starting data seeding...\n');
  
  try {
    // Initialize Firebase
    if (admin.apps.length === 0) {
      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.project_id
      });
    }
    
    const db = admin.firestore();
    console.log('✅ Firebase initialized successfully\n');
    
    // Seed Events
    console.log('📅 Seeding event data...');
    let eventCount = 0;
    
    for (const [eventType, eventInfo] of Object.entries(eventData)) {
      try {
        // Check if event already exists
        const existingEvents = await db.collection('events')
          .where('type', '==', eventType)
          .limit(1)
          .get();
        
        if (existingEvents.empty) {
          const eventDoc = {
            type: eventType,
            ...eventInfo,
            isStatic: true,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          };
          
          await db.collection('events').add(eventDoc);
          console.log(`  ✅ Seeded event: ${eventType}`);
          eventCount++;
        } else {
          console.log(`  ⏭️  Event ${eventType} already exists, skipping`);
        }
      } catch (error) {
        console.log(`  ❌ Failed to seed event ${eventType}:`, error.message);
      }
    }
    
    console.log(`🎯 Seeded ${eventCount} events\n`);
    
    // Seed Services
    console.log('🛍️  Seeding service data...');
    let serviceCount = 0;
    
    if (serviceData.services && Array.isArray(serviceData.services)) {
      for (const service of serviceData.services) {
        try {
          // Check if service already exists
          const existingServices = await db.collection('services')
            .where('id', '==', service.id)
            .limit(1)
            .get();
          
          if (existingServices.empty) {
            const serviceDoc = {
              ...service,
              isStatic: true,
              isActive: true,
              rating: service.rating || 0,
              reviewCount: service.reviewCount || 0,
              createdAt: admin.firestore.FieldValue.serverTimestamp(),
              updatedAt: admin.firestore.FieldValue.serverTimestamp()
            };
            
            await db.collection('services').add(serviceDoc);
            console.log(`  ✅ Seeded service: ${service.title}`);
            serviceCount++;
          } else {
            console.log(`  ⏭️  Service ${service.title} already exists, skipping`);
          }
        } catch (error) {
          console.log(`  ❌ Failed to seed service ${service.title}:`, error.message);
        }
      }
    }
    
    console.log(`🎯 Seeded ${serviceCount} services\n`);
    
    // Create a sample booking for testing
    console.log('🧪 Creating sample booking...');
    try {
      const existingBookings = await db.collection('bookings')
        .where('bookingNumber', '==', 'EVT-SAMPLE-001')
        .limit(1)
        .get();
      
      if (existingBookings.empty) {
        const sampleBooking = {
          bookingNumber: 'EVT-SAMPLE-001',
          eventType: 'Birthday',
          eventDate: '2024-12-25',
          eventTime: '18:00',
          location: 'Sample Venue, Mumbai',
          guestCount: '21-50 guests',
          services: ['Decoration', 'Catering', 'Photography'],
          contactName: 'John Doe',
          contactEmail: '<EMAIL>',
          contactPhone: '9876543210',
          status: 'pending',
          estimatedCost: 75000,
          specialRequests: 'Sample booking for testing purposes',
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        };
        
        await db.collection('bookings').add(sampleBooking);
        console.log('  ✅ Created sample booking');
      } else {
        console.log('  ⏭️  Sample booking already exists, skipping');
      }
    } catch (error) {
      console.log('  ⚠️  Could not create sample booking:', error.message);
    }
    
    console.log('\n🎉 Data seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Events seeded: ${eventCount}`);
    console.log(`- Services seeded: ${serviceCount}`);
    console.log('- Sample booking created');
    
    console.log('\n📝 Next steps:');
    console.log('1. Start your server: npm run dev');
    console.log('2. Test the API: curl http://localhost:5000/api/health');
    console.log('3. Check events: curl http://localhost:5000/api/events');
    console.log('4. Check services: curl http://localhost:5000/api/services');
    
  } catch (error) {
    console.error('\n❌ Data seeding failed:', error.message);
    
    if (error.code === 5) {
      console.log('\n🔧 NOT_FOUND error - Make sure Firestore database is created:');
      console.log('1. Go to: https://console.firebase.google.com/project/easemyevent-27729');
      console.log('2. Click "Firestore Database" and create the database');
    }
    
    if (error.code === 7) {
      console.log('\n🔧 PERMISSION_DENIED error - Check API and permissions:');
      console.log('1. Enable Firestore API');
      console.log('2. Check service account permissions');
    }
    
    throw error;
  }
}

// Run the seeding
if (require.main === module) {
  seedData()
    .then(() => {
      console.log('\n🏁 Seeding script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Seeding script failed:', error);
      process.exit(1);
    });
}

module.exports = seedData;
