# API Reference

Complete reference for all API endpoints in the Event App Backend.

## Base URL

- **Development**: `http://localhost:5000`
- **Production**: `https://your-domain.com`

## Authentication

Currently, the API uses Firebase Admin SDK for backend operations. Future versions will include:
- JWT token authentication for users
- API key authentication for external services
- Role-based access control

## Response Format

All API responses follow this consistent format:

### Success Response
```json
{
  "success": true,
  "data": {...},
  "count": 10,        // For array responses
  "total": 100,       // For paginated responses
  "message": "..."    // Optional success message
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "message": "User-friendly error message",
    "details": "Technical error details",
    "code": "ERROR_CODE"
  }
}
```

## HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | OK - Request successful |
| 201 | Created - Resource created successfully |
| 400 | Bad Request - Invalid request data |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Access denied |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource already exists |
| 422 | Unprocessable Entity - Validation failed |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |
| 503 | Service Unavailable - Service temporarily unavailable |

## Rate Limiting

- **Limit**: 100 requests per 15 minutes per IP
- **Headers**: Rate limit info included in response headers
- **Exceeded**: Returns 429 status with retry information

## Endpoints

### Health Check

#### Get Health Status
```http
GET /api/health
```

**Description**: Check API and database health status.

**Response**:
```json
{
  "success": true,
  "data": {
    "status": "OK",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600.123,
    "environment": "development",
    "version": "1.0.0",
    "services": {
      "api": "healthy",
      "database": "healthy"
    }
  }
}
```

#### Get System Information
```http
GET /api/health/system
```

**Description**: Get detailed system information.

**Response**:
```json
{
  "success": true,
  "data": {
    "node": {
      "version": "v18.17.0",
      "platform": "linux",
      "arch": "x64"
    },
    "memory": {
      "used": "45 MB",
      "total": "128 MB",
      "external": "2 MB"
    },
    "uptime": {
      "process": "3600 seconds",
      "system": "86400 seconds"
    },
    "loadAverage": [0.1, 0.2, 0.3],
    "cpus": 4
  }
}
```

### Events

#### Get All Events
```http
GET /api/events
```

**Query Parameters**:
- `limit` (optional): Number of results (1-100, default: 50)
- `offset` (optional): Number of results to skip (default: 0)
- `sort` (optional): Sort field (createdAt, updatedAt, eventType)
- `order` (optional): Sort order (asc, desc, default: desc)

**Response**:
```json
{
  "success": true,
  "count": 4,
  "total": 4,
  "data": [
    {
      "id": "doc_id_123",
      "type": "Birthday",
      "metadata": {
        "title": "Birthday Parties",
        "description": "Memorable birthday celebrations",
        "images": ["url1", "url2"],
        "icon": "cake",
        "path": "/event/booking?type=birthday"
      },
      "questions": [...],
      "serviceQuestions": {...},
      "isStatic": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### Get Event by Type
```http
GET /api/events/{type}
```

**Path Parameters**:
- `type`: Event type (Birthday, Corporate, HouseWarming, Conference)

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "doc_id_123",
    "type": "Birthday",
    "metadata": {...},
    "questions": [...],
    "serviceQuestions": {...}
  }
}
```

#### Get Event Questions
```http
GET /api/events/{type}/questions
```

**Description**: Get questions and service-specific questions for an event type.

**Response**:
```json
{
  "success": true,
  "data": {
    "questions": [
      {
        "id": "guestCount",
        "text": "How many guests are you expecting?",
        "type": "select",
        "options": ["1-20 guests", "21-50 guests"],
        "required": true
      }
    ],
    "serviceQuestions": {
      "Decoration": [
        {
          "id": "theme",
          "text": "What is the decoration theme?",
          "type": "text",
          "required": false
        }
      ]
    }
  }
}
```

#### Seed Event Data
```http
POST /api/events/seed
```

**Description**: Seed event data from static JSON files (admin operation).

### Services

#### Get All Services
```http
GET /api/services
```

**Query Parameters**:
- `limit` (optional): Number of results (1-100, default: 50)
- `offset` (optional): Number of results to skip (default: 0)
- `sort` (optional): Sort field (createdAt, rating, title)
- `order` (optional): Sort order (asc, desc, default: desc)

**Response**:
```json
{
  "success": true,
  "count": 12,
  "total": 12,
  "data": [
    {
      "id": "decoration",
      "title": "Decoration",
      "description": "Transform your venue with stunning decoration",
      "longDescription": "Detailed description...",
      "icon": "🎨",
      "category": "decoration",
      "features": ["Custom themes", "Premium materials"],
      "packages": [
        {
          "name": "Basic",
          "price": "₹25,000 - ₹50,000",
          "includes": ["Basic decoration", "Standard flowers"]
        }
      ],
      "rating": 4.5,
      "reviewCount": 150,
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### Get Service by ID
```http
GET /api/services/{id}
```

**Path Parameters**:
- `id`: Service identifier

#### Get Services by Category
```http
GET /api/services/category/{category}
```

**Path Parameters**:
- `category`: Service category (decoration, catering, photography, etc.)

#### Search Services
```http
GET /api/services/search
```

**Query Parameters**:
- `q`: Search term (required)
- `limit` (optional): Number of results (1-20, default: 20)

**Response**:
```json
{
  "success": true,
  "count": 5,
  "searchTerm": "decoration",
  "data": [...]
}
```

### Bookings

#### Create Booking
```http
POST /api/bookings
```

**Request Body**:
```json
{
  "eventType": "Birthday",
  "eventDate": "2024-12-25",
  "eventTime": "18:00",
  "location": "Sample Venue, Mumbai",
  "guestCount": "21-50 guests",
  "services": ["Decoration", "Catering"],
  "contactName": "John Doe",
  "contactEmail": "<EMAIL>",
  "contactPhone": "**********",
  "theme": "Superhero",
  "specialRequests": "Vegan food options"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Booking created successfully",
  "data": {
    "id": "booking_id_123",
    "bookingNumber": "EVT-123456-ABC123",
    "status": "pending",
    "estimatedCost": 75000
  }
}
```

#### Get All Bookings
```http
GET /api/bookings
```

**Query Parameters**:
- `limit` (optional): Number of results (1-100, default: 50)
- `offset` (optional): Number of results to skip (default: 0)
- `sort` (optional): Sort field (createdAt, eventDate, status)
- `order` (optional): Sort order (asc, desc, default: desc)
- `status` (optional): Filter by status (pending, confirmed, etc.)
- `eventType` (optional): Filter by event type
- `dateFrom` (optional): Filter bookings from date (YYYY-MM-DD)
- `dateTo` (optional): Filter bookings to date (YYYY-MM-DD)

#### Get Booking by ID
```http
GET /api/bookings/{id}
```

#### Update Booking
```http
PUT /api/bookings/{id}
```

**Request Body**: Same as create booking (all fields optional)

#### Update Booking Status
```http
PATCH /api/bookings/{id}/status
```

**Request Body**:
```json
{
  "status": "confirmed",
  "notes": "Venue confirmed, advance payment received"
}
```

**Valid Status Values**:
- `pending`: Initial booking status
- `confirmed`: Booking confirmed by admin
- `in-progress`: Event is currently happening
- `completed`: Event finished successfully
- `cancelled`: Booking cancelled

#### Delete Booking
```http
DELETE /api/bookings/{id}
```

## Validation Rules

### Event Type Validation
- Must be one of: `Birthday`, `Corporate`, `HouseWarming`, `Conference`

### Date/Time Validation
- `eventDate`: ISO 8601 date format (YYYY-MM-DD)
- `eventTime`: 24-hour format (HH:MM)

### Contact Information
- `contactEmail`: Valid email format
- `contactPhone`: Valid Indian mobile number format

### Guest Count
- Must be one of predefined ranges (e.g., "1-20 guests", "21-50 guests")

### Services
- Must be array with at least one service
- Services must exist in the system

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request data validation failed |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `DUPLICATE_RESOURCE` | Resource already exists |
| `FIREBASE_ERROR` | Database operation failed |
| `AUTHENTICATION_ERROR` | Authentication failed |
| `AUTHORIZATION_ERROR` | Access denied |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `INTERNAL_ERROR` | Unexpected server error |

## Examples

### Complete Booking Flow

1. **Get Event Types**:
   ```bash
   curl http://localhost:5000/api/events
   ```

2. **Get Event Questions**:
   ```bash
   curl http://localhost:5000/api/events/Birthday/questions
   ```

3. **Get Available Services**:
   ```bash
   curl http://localhost:5000/api/services
   ```

4. **Create Booking**:
   ```bash
   curl -X POST http://localhost:5000/api/bookings \
     -H "Content-Type: application/json" \
     -d '{
       "eventType": "Birthday",
       "eventDate": "2024-12-25",
       "eventTime": "18:00",
       "location": "Sample Venue",
       "guestCount": "21-50 guests",
       "services": ["Decoration", "Catering"],
       "contactName": "John Doe",
       "contactEmail": "<EMAIL>",
       "contactPhone": "**********"
     }'
   ```

5. **Check Booking Status**:
   ```bash
   curl http://localhost:5000/api/bookings/{booking_id}
   ```

### Search Services

```bash
# Search for decoration services
curl "http://localhost:5000/api/services/search?q=decoration&limit=5"

# Get services by category
curl http://localhost:5000/api/services/category/catering
```

### Health Monitoring

```bash
# Quick health check
curl http://localhost:5000/api/health

# Detailed system info
curl http://localhost:5000/api/health/system
```

## SDK/Client Libraries

### JavaScript/Node.js
```javascript
const axios = require('axios');

const apiClient = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Create booking
const booking = await apiClient.post('/bookings', bookingData);

// Get events
const events = await apiClient.get('/events');
```

### React Native
```javascript
const API_BASE_URL = 'http://localhost:5000/api';

const createBooking = async (bookingData) => {
  const response = await fetch(`${API_BASE_URL}/bookings`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(bookingData),
  });
  
  return response.json();
};
```

## Changelog

### v1.0.0 (Current)
- Initial API implementation
- Event management endpoints
- Service catalog endpoints
- Booking system endpoints
- Health monitoring endpoints
- Firebase integration
- Schema validation
- Error handling

### Planned Features
- User authentication endpoints
- Vendor management endpoints
- Review and rating system
- Payment integration
- Notification system
- File upload endpoints
- Advanced search and filtering
- Analytics endpoints

## Postman Collection

A Postman collection is available for testing all endpoints:

```json
{
  "info": {
    "name": "Event App Backend API",
    "description": "Complete API collection for Event App Backend"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:5000/api"
    }
  ],
  "item": [
    {
      "name": "Health Check",
      "request": {
        "method": "GET",
        "url": "{{baseUrl}}/health"
      }
    },
    {
      "name": "Get All Events",
      "request": {
        "method": "GET",
        "url": "{{baseUrl}}/events"
      }
    },
    {
      "name": "Create Booking",
      "request": {
        "method": "POST",
        "url": "{{baseUrl}}/bookings",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"eventType\": \"Birthday\",\n  \"eventDate\": \"2024-12-25\",\n  \"eventTime\": \"18:00\",\n  \"location\": \"Sample Venue\",\n  \"guestCount\": \"21-50 guests\",\n  \"services\": [\"Decoration\", \"Catering\"],\n  \"contactName\": \"John Doe\",\n  \"contactEmail\": \"<EMAIL>\",\n  \"contactPhone\": \"**********\"\n}"
        }
      }
    }
  ]
}
