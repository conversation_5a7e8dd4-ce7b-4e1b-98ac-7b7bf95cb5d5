{"name": "expo-manifests", "version": "0.16.5", "description": "Code to parse and use Expo and Expo Updates manifests.", "main": "build/Manifests.js", "types": "build/Manifests.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-manifests"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-manifests"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/manifests/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"@expo/config": "~11.0.10", "expo-json-utils": "~0.15.0"}, "devDependencies": {"expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*"}, "gitHead": "49c9d53cf0a9fc8179d1c8f5268beadd141f70ca"}