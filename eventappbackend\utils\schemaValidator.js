const schemas = require('../models/schemas');

/**
 * Schema validation utility for Firestore documents
 * Validates data against defined schemas before saving to database
 */

class SchemaValidator {
  /**
   * Validate data against a schema
   * @param {Object} data - Data to validate
   * @param {Object} schema - Schema definition
   * @param {string} path - Current path for error reporting
   * @returns {Object} - { isValid: boolean, errors: string[] }
   */
  static validate(data, schema, path = '') {
    const errors = [];
    
    try {
      this._validateObject(data, schema, path, errors);
    } catch (error) {
      errors.push(`Validation error at ${path}: ${error.message}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Validate object against schema
   */
  static _validateObject(data, schema, path, errors) {
    if (!data || typeof data !== 'object') {
      errors.push(`${path}: Expected object, got ${typeof data}`);
      return;
    }
    
    // Check required fields
    Object.keys(schema).forEach(key => {
      const fieldSchema = schema[key];
      const fieldPath = path ? `${path}.${key}` : key;
      
      if (fieldSchema.required && !(key in data)) {
        errors.push(`${fieldPath}: Required field missing`);
        return;
      }
      
      if (key in data) {
        this._validateField(data[key], fieldSchema, fieldPath, errors);
      }
    });
    
    // Check for unexpected fields (optional - can be disabled)
    if (process.env.STRICT_SCHEMA_VALIDATION === 'true') {
      Object.keys(data).forEach(key => {
        if (!(key in schema)) {
          errors.push(`${path ? `${path}.${key}` : key}: Unexpected field`);
        }
      });
    }
  }
  
  /**
   * Validate individual field
   */
  static _validateField(value, fieldSchema, path, errors) {
    // Handle null/undefined
    if (value === null || value === undefined) {
      if (fieldSchema.required) {
        errors.push(`${path}: Required field is null/undefined`);
      }
      return;
    }
    
    // Type validation
    if (!this._validateType(value, fieldSchema.type)) {
      errors.push(`${path}: Expected ${fieldSchema.type}, got ${typeof value}`);
      return;
    }
    
    // Enum validation
    if (fieldSchema.enum && !fieldSchema.enum.includes(value)) {
      errors.push(`${path}: Value must be one of [${fieldSchema.enum.join(', ')}]`);
    }
    
    // String validations
    if (fieldSchema.type === 'string') {
      this._validateString(value, fieldSchema, path, errors);
    }
    
    // Number validations
    if (fieldSchema.type === 'number') {
      this._validateNumber(value, fieldSchema, path, errors);
    }
    
    // Array validations
    if (fieldSchema.type === 'array') {
      this._validateArray(value, fieldSchema, path, errors);
    }
    
    // Object validations
    if (fieldSchema.type === 'object') {
      if (fieldSchema.properties) {
        this._validateObject(value, fieldSchema.properties, path, errors);
      }
    }
  }
  
  /**
   * Validate type
   */
  static _validateType(value, expectedType) {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && !Array.isArray(value);
      case 'timestamp':
        return value instanceof Date || 
               typeof value === 'string' || 
               (typeof value === 'object' && value._seconds); // Firestore Timestamp
      default:
        return true;
    }
  }
  
  /**
   * Validate string field
   */
  static _validateString(value, fieldSchema, path, errors) {
    if (fieldSchema.minLength && value.length < fieldSchema.minLength) {
      errors.push(`${path}: Minimum length is ${fieldSchema.minLength}`);
    }
    
    if (fieldSchema.maxLength && value.length > fieldSchema.maxLength) {
      errors.push(`${path}: Maximum length is ${fieldSchema.maxLength}`);
    }
    
    if (fieldSchema.format) {
      if (!this._validateFormat(value, fieldSchema.format)) {
        errors.push(`${path}: Invalid ${fieldSchema.format} format`);
      }
    }
    
    if (fieldSchema.pattern) {
      const regex = new RegExp(fieldSchema.pattern);
      if (!regex.test(value)) {
        errors.push(`${path}: Does not match required pattern`);
      }
    }
  }
  
  /**
   * Validate number field
   */
  static _validateNumber(value, fieldSchema, path, errors) {
    if (fieldSchema.min !== undefined && value < fieldSchema.min) {
      errors.push(`${path}: Minimum value is ${fieldSchema.min}`);
    }
    
    if (fieldSchema.max !== undefined && value > fieldSchema.max) {
      errors.push(`${path}: Maximum value is ${fieldSchema.max}`);
    }
  }
  
  /**
   * Validate array field
   */
  static _validateArray(value, fieldSchema, path, errors) {
    if (fieldSchema.minItems && value.length < fieldSchema.minItems) {
      errors.push(`${path}: Minimum items is ${fieldSchema.minItems}`);
    }
    
    if (fieldSchema.maxItems && value.length > fieldSchema.maxItems) {
      errors.push(`${path}: Maximum items is ${fieldSchema.maxItems}`);
    }
    
    if (fieldSchema.items) {
      value.forEach((item, index) => {
        this._validateField(item, fieldSchema.items, `${path}[${index}]`, errors);
      });
    }
  }
  
  /**
   * Validate format
   */
  static _validateFormat(value, format) {
    switch (format) {
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case 'date':
        return /^\d{4}-\d{2}-\d{2}$/.test(value) && !isNaN(Date.parse(value));
      case 'time':
        return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(value);
      case 'url':
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      case 'phone':
        return /^[+]?[\d\s\-\(\)]{10,}$/.test(value);
      default:
        return true;
    }
  }
  
  /**
   * Validate specific document types
   */
  static validateEvent(data) {
    return this.validate(data, schemas.eventSchema);
  }
  
  static validateService(data) {
    return this.validate(data, schemas.serviceSchema);
  }
  
  static validateBooking(data) {
    return this.validate(data, schemas.bookingSchema);
  }
  
  static validateUser(data) {
    return this.validate(data, schemas.userSchema);
  }
  
  static validateVendor(data) {
    return this.validate(data, schemas.vendorSchema);
  }
  
  static validateReview(data) {
    return this.validate(data, schemas.reviewSchema);
  }
  
  /**
   * Sanitize data by removing undefined values and applying defaults
   */
  static sanitize(data, schema) {
    const sanitized = { ...data };
    
    Object.keys(schema).forEach(key => {
      const fieldSchema = schema[key];
      
      // Remove undefined values
      if (sanitized[key] === undefined) {
        delete sanitized[key];
      }
      
      // Apply defaults
      if (!(key in sanitized) && fieldSchema.default !== undefined) {
        sanitized[key] = fieldSchema.default;
      }
      
      // Sanitize nested objects
      if (fieldSchema.type === 'object' && fieldSchema.properties && sanitized[key]) {
        sanitized[key] = this.sanitize(sanitized[key], fieldSchema.properties);
      }
    });
    
    return sanitized;
  }
}

module.exports = SchemaValidator;
