{"name": "expo-pwa", "version": "0.0.127", "main": "build", "preferGlobal": true, "keywords": ["expo", "pwa", "react-native", "react-native-web"], "description": "Create PWA resources for Expo projects", "repository": {"type": "git", "url": "https://github.com/expo/expo-cli.git", "directory": "packages/pwa"}, "author": "Expo <<EMAIL>>", "license": "MIT", "bin": {"expo-pwa": "./build/cli.js"}, "files": ["build"], "scripts": {"prepare": "yarn run clean && yarn run build", "test": "jest", "lint": "eslint .", "watch": "tsc --watch --preserveWatchOutput", "build": "tsc", "clean": "rimraf build ./tsconfig.tsbuildinfo"}, "dependencies": {"@expo/image-utils": "0.3.23", "chalk": "^4.0.0", "commander": "2.20.0", "update-check": "1.5.3"}, "peerDependencies": {"expo": "*"}}