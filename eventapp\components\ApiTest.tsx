import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { API_CONFIG } from '../constants/Api';
import { servicesApi } from '../lib/api/services';

export default function ApiTest() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testApiConnection = async () => {
    setLoading(true);
    setResult('Testing API connection...\n');
    
    try {
      // Log the API configuration
      setResult(prev => prev + `API Base URL: ${API_CONFIG.BASE_URL}\n`);
      setResult(prev => prev + `Services endpoint: ${API_CONFIG.ENDPOINTS.SERVICES}\n\n`);
      
      // Test basic fetch
      setResult(prev => prev + 'Testing basic fetch...\n');
      const response = await fetch(`${API_CONFIG.BASE_URL}/services`);
      setResult(prev => prev + `Response status: ${response.status}\n`);
      setResult(prev => prev + `Response ok: ${response.ok}\n`);
      
      if (response.ok) {
        const data = await response.json();
        setResult(prev => prev + `Success! Got ${data.data?.length || 0} services\n`);
        setResult(prev => prev + `First service: ${data.data?.[0]?.title || 'None'}\n\n`);
      } else {
        const errorText = await response.text();
        setResult(prev => prev + `Error response: ${errorText}\n\n`);
      }
      
      // Test using our API helper
      setResult(prev => prev + 'Testing with API helper...\n');
      const apiResult = await servicesApi.getAll();
      setResult(prev => prev + `API helper success: ${apiResult.success}\n`);
      
      if (apiResult.success && apiResult.data) {
        setResult(prev => prev + `Got ${apiResult.data.length} services via API helper\n`);
      } else {
        setResult(prev => prev + `API helper error: ${apiResult.error?.message}\n`);
      }
      
    } catch (error) {
      setResult(prev => prev + `Network error: ${error instanceof Error ? error.message : 'Unknown error'}\n`);
      console.error('API test error:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setResult('');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Connection Test</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, loading && styles.buttonDisabled]} 
          onPress={testApiConnection}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Testing...' : 'Test API Connection'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.clearButton} onPress={clearResults}>
          <Text style={styles.clearButtonText}>Clear</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.resultContainer}>
        <Text style={styles.resultText}>{result}</Text>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 20,
  },
  button: {
    flex: 1,
    backgroundColor: '#FF3366',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  clearButton: {
    backgroundColor: '#666',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    minWidth: 80,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  resultContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 15,
  },
  resultText: {
    fontFamily: 'monospace',
    fontSize: 12,
    lineHeight: 18,
  },
});
