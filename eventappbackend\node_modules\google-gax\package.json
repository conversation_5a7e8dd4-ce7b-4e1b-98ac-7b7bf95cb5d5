{"name": "google-gax", "version": "3.6.1", "description": "Google API Extensions", "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src", "build/tools/compileProtos.js", "build/tools/minify.js", "build/protos/"], "bin": {"compileProtos": "build/tools/compileProtos.js", "minifyProtoJson": "build/tools/minify.js"}, "dependencies": {"@grpc/grpc-js": "~1.8.0", "@grpc/proto-loader": "^0.7.0", "@types/long": "^4.0.0", "@types/rimraf": "^3.0.2", "abort-controller": "^3.0.0", "duplexify": "^4.0.0", "fast-text-encoding": "^1.0.3", "google-auth-library": "^8.0.2", "is-stream-ended": "^0.1.4", "node-fetch": "^2.6.1", "object-hash": "^3.0.0", "proto3-json-serializer": "^1.0.0", "protobufjs": "7.2.4", "protobufjs-cli": "1.1.1", "retry-request": "^5.0.0"}, "devDependencies": {"@compodoc/compodoc": "1.1.19", "@types/mocha": "^9.0.0", "@types/ncp": "^2.0.1", "@types/node": "^18.0.0", "@types/node-fetch": "^2.5.4", "@types/object-hash": "^3.0.0", "@types/proxyquire": "^1.3.28", "@types/pumpify": "^1.4.1", "@types/sinon": "^10.0.0", "@types/uglify-js": "^3.17.0", "c8": "^7.0.0", "codecov": "^3.1.0", "execa": "^5.0.0", "google-proto-files": "^3.0.0", "gts": "^3.1.0", "linkinator": "^4.0.0", "long": "^4.0.0", "mkdirp": "^2.0.0", "mocha": "^9.0.0", "ncp": "^2.0.0", "null-loader": "^4.0.0", "proxyquire": "^2.0.1", "pumpify": "^2.0.0", "rimraf": "^3.0.2", "sinon": "^15.0.0", "stream-events": "^1.0.4", "ts-loader": "^8.0.0", "typescript": "^4.6.4", "uglify-js": "^3.17.0", "walkdir": "^0.4.0", "webpack": "^4.0.0", "webpack-cli": "^4.0.0"}, "scripts": {"docs": "compodoc src/", "pretest": "npm run prepare", "test": "c8 mocha build/test/unit", "lint": "gts check", "clean": "gts clean", "compile": "tsc -p . && cp src/*.json build/src && cp -r test/fixtures build/test && cp -r protos build/", "compile-operation-protos": "pbjs -t json google/longrunning/operations.proto -p ./protos > protos/operations.json && pbjs -t static-module -r operations_protos google/longrunning/operations.proto -p ./protos > protos/operations.js && pbts protos/operations.js -o protos/operations.d.ts", "compile-compute-operations-protos": "pbjs -t json google/longrunning/compute_operations.proto -p ./protos > protos/compute_operations.json && pbjs -t static-module -r compute_operations_protos google/longrunning/compute_operations.proto -p ./protos > protos/compute_operations.js && pbts protos/compute_operations.js -o protos/compute_operations.d.ts", "compile-iam-protos": "pbjs -t json google/iam/v1/iam_policy.proto google/iam/v1/options.proto google/iam/v1/policy.proto google/iam/v1/logging/audit_data.proto -p ./protos > protos/iam_service.json && pbjs -t static-module -r iam_protos google/iam/v1/iam_policy.proto google/iam/v1/options.proto google/iam/v1/policy.proto google/iam/v1/logging/audit_data.proto -p ./protos > protos/iam_service.js && pbts protos/iam_service.js -o protos/iam_service.d.ts", "compile-location-protos": "pbjs -t json google/cloud/location/locations.proto -p ./protos > protos/locations.json && pbjs -t static-module -r locations_protos google/cloud/location/locations.proto -p ./protos > protos/locations.js && pbts protos/locations.js -o protos/locations.d.ts", "compile-status-protos": "pbjs -t json google/rpc/status.proto google/rpc/error_details.proto -p ./protos > protos/status.json", "compile-http-protos": "pbjs -t static-module -r http_proto --keep-case google/api/http.proto -p ./protos > protos/http.js && pbts protos/http.js -o protos/http.d.ts", "compile-showcase-proto": "pbjs -t json google/showcase/v1beta1/echo.proto google/showcase/v1beta1/identity.proto google/showcase/v1beta1/messaging.proto google/showcase/v1beta1/testing.proto -p ./protos > test/fixtures/google-gax-packaging-test-app/protos/protos.json && pbjs -t static-module -r showcase_protos google/showcase/v1beta1/echo.proto google/showcase/v1beta1/identity.proto google/showcase/v1beta1/messaging.proto google/showcase/v1beta1/testing.proto -p ./protos > test/fixtures/google-gax-packaging-test-app/protos/protos.js && pbts test/fixtures/google-gax-packaging-test-app/protos/protos.js -o test/fixtures/google-gax-packaging-test-app/protos/protos.d.ts", "fix": "gts fix", "prepare": "npm run compile && node ./build/tools/prepublish.js && mkdirp build/protos && cp -r protos/* build/protos/ && npm run minify-proto-json", "system-test": "c8 mocha build/test/system-test --timeout 600000 && npm run test-application", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "browser-test": "cd test/browser-test && npm run prefetch && npm install && npm test", "test-application": "cd test/test-application && npm run prefetch && npm install && npm start", "prelint": "cd samples; npm link ../; npm install", "precompile": "gts clean", "update-protos": "node ./build/tools/listProtos.js", "minify-proto-json": "node ./build/tools/minify.js"}, "repository": "googleapis/gax-nodejs", "keywords": ["grpc"], "author": "Google API Authors", "license": "Apache-2.0", "bugs": {"url": "https://github.com/googleapis/gax-nodejs/issues"}, "homepage": "https://github.com/googleapis/gax-nodejs#readme", "engines": {"node": ">=12"}, "browser": "build/src/fallback.js"}